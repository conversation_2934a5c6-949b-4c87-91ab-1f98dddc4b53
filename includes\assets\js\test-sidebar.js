/**
 * Test script to check sidebar functionality
 */

console.log("test-sidebar.js loaded");

// Check if elements exist after DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  console.log("DOM loaded in test-sidebar.js");

  // Check sidebar toggle button
  const sidebarToggle = document.getElementById("sidebar-toggle");
  console.log("Sidebar toggle button exists:", !!sidebarToggle);

  // Check submenu toggles
  const submenuToggles = document.querySelectorAll(".submenu-toggle");
  console.log("Found submenu toggles in test script:", submenuToggles.length);

  // Add direct click handlers to submenu toggles
  submenuToggles.forEach((toggle, index) => {
    console.log(`Submenu toggle ${index} href:`, toggle.getAttribute("href"));

    // Add a direct click event listener
    toggle.addEventListener("click", function (e) {
      e.preventDefault();
      console.log(
        `Submenu toggle ${index} clicked directly from test-sidebar.js`
      );

      // Get the submenu
      const submenu = this.nextElementSibling;
      console.log("Submenu found:", !!submenu);

      if (submenu) {
        const isHidden = submenu.classList.contains("hidden");
        console.log("Submenu is hidden:", isHidden);

        // Toggle the submenu visibility
        if (isHidden) {
          submenu.classList.remove("hidden");
        } else {
          submenu.classList.add("hidden");
        }

        // Toggle the chevron icon
        const chevron = this.querySelector("svg:last-child");
        if (chevron) {
          if (isHidden) {
            chevron.classList.add("rotate-90");
          } else {
            chevron.classList.remove("rotate-90");
          }
        }
      }
    });
  });
});
