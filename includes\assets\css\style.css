/* Custom styles beyond Tailwind */

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Apple-inspired focus styles */
input:focus,
button:focus {
  outline: none;
}

/* Custom animation for hover effects */
.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Custom placeholder color */
::placeholder {
  color: #9ca3af;
  opacity: 0.7;
}

/* Custom scrollbar (webkit browsers) */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Apple-inspired loading animation */
.apple-loader {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 113, 227, 0.2);
  border-radius: 50%;
  border-top-color: #0071e3;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive font size adjustments */
@media (max-width: 640px) {
  h1 {
    font-size: 1.75rem;
  }
  h2 {
    font-size: 1.5rem;
  }
}

/* Sidebar Styles */
.sidebar {
  width: 250px;
  transition: width 0.3s ease-in-out;
}

.sidebar-collapsed {
  width: 70px;
}

.content-expanded {
  margin-left: 70px;
}

#main-content {
  margin-left: 250px;
  transition: margin-left 0.3s ease-in-out;
}

.sidebar-menu li {
  position: relative;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #6b7280;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.sidebar-item:hover {
  background-color: rgba(0, 113, 227, 0.1);
  color: #0071e3;
}

.sidebar-item.active {
  background-color: rgba(0, 113, 227, 0.1);
  color: #0071e3;
  font-weight: 500;
}

.sidebar-item svg {
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.75rem;
  transition: transform 0.2s ease-in-out;
}

.sidebar-collapsed .sidebar-item {
  justify-content: center;
  padding: 0.75rem;
  width: 100%;
  text-align: center;
}

.sidebar-collapsed .sidebar-item svg {
  margin-right: 0;
  margin-left: 0;
}

/* Hide all text labels when sidebar is collapsed */
.sidebar-collapsed .sidebar-item-label {
  display: none !important;
  width: 0;
  height: 0;
  opacity: 0;
  visibility: hidden;
  overflow: hidden;
}

/* Hide submenu arrows when sidebar is collapsed */
.sidebar-collapsed .submenu-toggle svg:last-child {
  display: none;
}

/* Center the small logo text when sidebar is collapsed */
.sidebar-collapsed #sidebar-logo-small {
  display: flex;
  justify-content: center;
  width: 100%;
}

/* Sidebar toggle button styles */
.sidebar-toggle-btn {
  transition: left 0.3s ease-in-out;
}

/* Move toggle button when sidebar is collapsed */
.sidebar-collapsed + .sidebar-toggle-btn,
.sidebar-collapsed ~ .sidebar-toggle-btn {
  left: 80px !important;
}

/* Hover submenu styles for collapsed sidebar */
.sidebar-collapsed .sidebar-menu li {
  position: relative;
}

.sidebar-collapsed .sidebar-menu li:hover .submenu {
  display: block;
  position: absolute;
  left: 70px;
  top: 0;
  min-width: 200px;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 40;
  padding: 0.5rem;
}

.sidebar-item-label {
  transition: all 0.3s ease-in-out;
  white-space: nowrap;
  display: inline-block;
}

/* Make sure hidden sidebar labels are properly hidden */
.sidebar-item-label.hidden {
  display: none !important;
}

/* Make sure visible sidebar labels are properly displayed */
.sidebar-item-label:not(.hidden) {
  display: inline-block !important;
}

.submenu {
  margin-left: 2.25rem;
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

/* Make sure hidden class works properly */
.submenu.hidden {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Make sure visible submenus are displayed properly */
.submenu:not(.hidden) {
  display: block !important;
  visibility: visible !important;
  height: auto !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

.submenu-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: #6b7280;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
}

.submenu-item:hover {
  background-color: rgba(0, 113, 227, 0.05);
  color: #0071e3;
}

.submenu-item.active {
  color: #0071e3;
  font-weight: 500;
}

.submenu-item::before {
  content: "";
  display: inline-block;
  width: 0.25rem;
  height: 0.25rem;
  border-radius: 50%;
  background-color: currentColor;
  margin-right: 0.5rem;
}

.submenu-toggle svg {
  transition: transform 0.2s ease-in-out;
}

.rotate-90 {
  transform: rotate(90deg);
}

.active-menu-item {
  color: #0071e3 !important;
  font-weight: 500;
}

/* Mobile sidebar */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -250px;
    z-index: 50;
    height: 100vh;
    transition: left 0.3s ease-in-out;
  }

  .sidebar.sidebar-open {
    left: 0;
  }

  /* Make sure the sidebar is visible on mobile regardless of collapsed state */
  .sidebar-collapsed.sidebar-open {
    width: 250px !important;
  }

  #main-content {
    margin-left: 0 !important;
  }

  .content-expanded {
    margin-left: 0 !important;
  }

  .sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;
  }

  .sidebar-overlay.show {
    display: block;
  }

  /* Hide the sidebar toggle button on mobile */
  #sidebar-toggle-container {
    display: none;
  }
}
