/**
 * Submenu Fix - Ensures submenu toggles work properly
 * This is a more aggressive fix that overrides the default behavior
 */

// Wait for the page to fully load
window.addEventListener("load", function () {
  applySubmenuFix();
});

// Also try on DOMContentLoaded in case window load is too late
document.addEventListener("DOMContentLoaded", function () {
  applySubmenuFix();

  // Also apply after a short delay to ensure it runs after other scripts
  setTimeout(applySubmenuFix, 500);
});

function applySubmenuFix() {
  // Remove all existing click handlers from submenu toggles
  const submenuToggles = document.querySelectorAll(".submenu-toggle");

  // Clone and replace all submenu toggles to remove existing event listeners
  submenuToggles.forEach((toggle, index) => {
    const clone = toggle.cloneNode(true);
    toggle.parentNode.replaceChild(clone, toggle);

    // Add our own click handler
    clone.addEventListener("click", function (e) {
      e.preventDefault();
      e.stopPropagation(); // Stop event bubbling

      // Get the submenu
      const submenu = this.nextElementSibling;

      if (submenu) {
        // Force toggle by checking display style
        const isVisible = window.getComputedStyle(submenu).display !== "none";

        // Toggle the submenu visibility
        if (isVisible) {
          // Hide the submenu
          submenu.style.display = "none";
          submenu.classList.add("hidden");
        } else {
          // Show the submenu
          submenu.style.display = "block";
          submenu.classList.remove("hidden");
        }

        // Toggle the chevron icon
        const chevron = this.querySelector("svg:last-child");
        if (chevron) {
          if (!isVisible) {
            chevron.style.transform = "rotate(90deg)";
            chevron.classList.add("rotate-90");
          } else {
            chevron.style.transform = "rotate(0deg)";
            chevron.classList.remove("rotate-90");
          }
        }

        // Close other submenus
        document
          .querySelectorAll(".submenu-toggle")
          .forEach((otherToggle, otherIndex) => {
            if (otherIndex !== index) {
              const otherSubmenu = otherToggle.nextElementSibling;
              const otherChevron = otherToggle.querySelector("svg:last-child");

              if (otherSubmenu) {
                otherSubmenu.style.display = "none";
                otherSubmenu.classList.add("hidden");
              }

              if (otherChevron) {
                otherChevron.style.transform = "rotate(0deg)";
                otherChevron.classList.remove("rotate-90");
              }
            }
          });
      }

      return false; // Prevent default and stop propagation
    });
  });
}
