<?php
// Set page variables
$pageTitle = "Dashboard";
$pageHeader = "Dashboard";
$pageDescription = "Welcome to Alyanka CRM. Here's what's happening with your business today.";
$activePage = "dashboard.php";

// Include the layout header
include 'layout.php';
?>

<!-- Dashboard Content -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
    <!-- Customers Card -->
    <div class="bg-white overflow-hidden rounded-2xl shadow-apple">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                    <svg class="h-6 w-6 text-apple-blue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                            Total Customers
                        </dt>
                        <dd>
                            <div class="text-lg font-semibold text-apple-dark">
                                1,482
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="customers.php" class="font-medium text-apple-blue hover:text-blue-600">
                    View all
                </a>
            </div>
        </div>
    </div>

    <!-- Revenue Card -->
    <div class="bg-white overflow-hidden rounded-2xl shadow-apple">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
                    <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                            Revenue
                        </dt>
                        <dd>
                            <div class="text-lg font-semibold text-apple-dark">
                                $24,500
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="report-sales.php" class="font-medium text-apple-blue hover:text-blue-600">
                    View report
                </a>
            </div>
        </div>
    </div>

    <!-- Projects Card -->
    <div class="bg-white overflow-hidden rounded-2xl shadow-apple">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-purple-100 rounded-md p-3">
                    <svg class="h-6 w-6 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                            Active Projects
                        </dt>
                        <dd>
                            <div class="text-lg font-semibold text-apple-dark">
                                12
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="projects.php" class="font-medium text-apple-blue hover:text-blue-600">
                    View all
                </a>
            </div>
        </div>
    </div>

    <!-- Tasks Card -->
    <div class="bg-white overflow-hidden rounded-2xl shadow-apple">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-yellow-100 rounded-md p-3">
                    <svg class="h-6 w-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                            Pending Tasks
                        </dt>
                        <dd>
                            <div class="text-lg font-semibold text-apple-dark">
                                18
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="tasks.php" class="font-medium text-apple-blue hover:text-blue-600">
                    View all
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="mt-6">
    <h3 class="text-lg font-medium text-apple-dark mb-4">Recent Activity</h3>
    <div class="bg-white shadow-apple rounded-2xl overflow-hidden">
        <ul class="divide-y divide-gray-200">
            <li class="p-4 hover:bg-gray-50">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=John+Doe&background=0071e3&color=fff" alt="User">
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-apple-dark truncate">
                            John Doe added a new customer
                        </p>
                        <p class="text-sm text-gray-500">
                            Apple Inc.
                        </p>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">2h ago</span>
                    </div>
                </div>
            </li>
            <li class="p-4 hover:bg-gray-50">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Sarah+Smith&background=0071e3&color=fff" alt="User">
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-apple-dark truncate">
                            Sarah Smith completed a project
                        </p>
                        <p class="text-sm text-gray-500">
                            Website Redesign
                        </p>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">5h ago</span>
                    </div>
                </div>
            </li>
            <li class="p-4 hover:bg-gray-50">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Mike+Johnson&background=0071e3&color=fff" alt="User">
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-apple-dark truncate">
                            Mike Johnson sent an invoice
                        </p>
                        <p class="text-sm text-gray-500">
                            $3,400 - Microsoft Corp.
                        </p>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">Yesterday</span>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<?php
// Set page-specific script
$pageScript = "
    // Dashboard specific JavaScript can go here
    console.log('Dashboard page loaded');
";

// Include the footer
include 'footer.php';
?>
index current code and help me to create a proper erp worth more than 50lac indian ruppes fiirst we start with just creating the layout andwe dont use any react componens only html css js ajax php mysql tailwindcss and follow proper folder structure backend , frontend , inbackend api , in fronend module wise folder like purchase , account , sales , finance and all other departs