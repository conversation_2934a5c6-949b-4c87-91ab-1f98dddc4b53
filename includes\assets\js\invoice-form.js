/**
 * Invoice Form Functionality
 * 
 * Provides dynamic form handling and calculations for the invoice creation page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize form elements
    initializeInvoiceForm();

    // Add event listeners for the "Add" buttons
    document.getElementById('addDeliverable').addEventListener('click', addDeliverable);
    document.getElementById('addMilestone').addEventListener('click', addMilestone);
    document.getElementById('addInvoiceItem').addEventListener('click', addInvoiceItem);

    // Add event listeners for calculation
    document.addEventListener('change', function(e) {
        // Check if the changed element is an invoice item input
        if (e.target.classList.contains('item-quantity') || e.target.classList.contains('item-price')) {
            calculateItemTotal(e.target);
            updateInvoiceTotals();
        }

        // Recalculate totals when tax rate or discount changes
        if (e.target.id === 'tax_rate' || e.target.id === 'discount_amount') {
            updateInvoiceTotals();
        }
    });

    // Initialize calculations
    updateInvoiceTotals();
});

// Initialize form elements and calculations
function initializeInvoiceForm() {
    // Initialize any item total calculations
    document.querySelectorAll('.invoice-item').forEach(function(item) {
        const quantityInput = item.querySelector('.item-quantity');
        if (quantityInput) {
            calculateItemTotal(quantityInput);
        }
    });

    // Set up initial invoice totals
    updateInvoiceTotals();
}

// Add a new deliverable field
function addDeliverable() {
    const deliverableItems = document.getElementById('deliverableItems');
    const deliverableCount = deliverableItems.querySelectorAll('.deliverable-item').length;
    
    const newDeliverable = document.createElement('div');
    newDeliverable.className = 'deliverable-item';
    newDeliverable.innerHTML = `
        <div class="bg-gray-50 p-4 rounded-lg">
            <div class="flex justify-between">
                <div class="flex-1">
                    <label for="deliverables[${deliverableCount}]" class="block text-sm font-medium text-gray-700 mb-1">Deliverable*</label>
                    <input type="text" id="deliverables[${deliverableCount}]" name="deliverables[]" class="w-full border-gray-300 rounded-md shadow-sm focus:border-apple-blue focus:ring focus:ring-apple-blue focus:ring-opacity-50" placeholder="e.g., Website Design, Mobile App, etc." required>
                </div>
                <div class="ml-4 flex items-end">
                    <button type="button" onclick="removeDeliverable(this)" class="inline-flex items-center p-1 border border-transparent rounded-full text-red-600 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    deliverableItems.appendChild(newDeliverable);
}

// Remove a deliverable field
function removeDeliverable(button) {
    const deliverableItem = button.closest('.deliverable-item');
    deliverableItem.remove();
}

// Add a new milestone field
function addMilestone() {
    const milestoneItems = document.getElementById('milestoneItems');
    const milestoneCount = milestoneItems.querySelectorAll('.milestone-item').length;
    
    const newMilestone = document.createElement('div');
    newMilestone.className = 'milestone-item';
    newMilestone.innerHTML = `
        <div class="bg-gray-50 p-4 rounded-lg">
            <div class="flex justify-between mb-4">
                <h5 class="text-sm font-medium text-gray-700">New Milestone</h5>
                <button type="button" onclick="removeMilestone(this)" class="inline-flex items-center p-1 border border-transparent rounded-full text-red-600 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="milestone_name_${milestoneCount}" class="block text-sm font-medium text-gray-700 mb-1">Milestone Name*</label>
                    <input type="text" id="milestone_name_${milestoneCount}" name="milestone_name[]" class="w-full border-gray-300 rounded-md shadow-sm focus:border-apple-blue focus:ring focus:ring-apple-blue focus:ring-opacity-50" placeholder="e.g., Project Kickoff" required>
                </div>
                <div>
                    <label for="milestone_amount_${milestoneCount}" class="block text-sm font-medium text-gray-700 mb-1">Amount (₹)*</label>
                    <input type="number" id="milestone_amount_${milestoneCount}" name="milestone_amount[]" class="w-full border-gray-300 rounded-md shadow-sm focus:border-apple-blue focus:ring focus:ring-apple-blue focus:ring-opacity-50" placeholder="0.00" min="0" step="0.01" required>
                </div>
                <div>
                    <label for="milestone_status_${milestoneCount}" class="block text-sm font-medium text-gray-700 mb-1">Status*</label>
                    <select id="milestone_status_${milestoneCount}" name="milestone_status[]" class="w-full border-gray-300 rounded-md shadow-sm focus:border-apple-blue focus:ring focus:ring-apple-blue focus:ring-opacity-50" required>
                        <option value="not_started">Not Started</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label for="milestone_description_${milestoneCount}" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <input type="text" id="milestone_description_${milestoneCount}" name="milestone_description[]" class="w-full border-gray-300 rounded-md shadow-sm focus:border-apple-blue focus:ring focus:ring-apple-blue focus:ring-opacity-50" placeholder="Brief description of this milestone">
                </div>
                <div>
                    <label for="milestone_due_date_${milestoneCount}" class="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                    <input type="date" id="milestone_due_date_${milestoneCount}" name="milestone_due_date[]" class="w-full border-gray-300 rounded-md shadow-sm focus:border-apple-blue focus:ring focus:ring-apple-blue focus:ring-opacity-50">
                </div>
            </div>
        </div>
    `;
    
    milestoneItems.appendChild(newMilestone);
}

// Remove a milestone field
function removeMilestone(button) {
    const milestoneItem = button.closest('.milestone-item');
    milestoneItem.remove();
}

// Add a new invoice item field
function addInvoiceItem() {
    const invoiceItems = document.getElementById('invoiceItems');
    const itemCount = invoiceItems.querySelectorAll('.invoice-item').length;
    
    const newItem = document.createElement('div');
    newItem.className = 'invoice-item';
    newItem.innerHTML = `
        <div class="bg-gray-50 p-4 rounded-lg">
            <div class="flex justify-between mb-4">
                <h5 class="text-sm font-medium text-gray-700">New Item</h5>
                <button type="button" onclick="removeInvoiceItem(this)" class="inline-flex items-center p-1 border border-transparent rounded-full text-red-600 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="md:col-span-2">
                    <label for="item_name_${itemCount}" class="block text-sm font-medium text-gray-700 mb-1">Item Name*</label>
                    <input type="text" id="item_name_${itemCount}" name="item_name[]" class="w-full border-gray-300 rounded-md shadow-sm focus:border-apple-blue focus:ring focus:ring-apple-blue focus:ring-opacity-50" placeholder="e.g., Web Development" required>
                </div>
                <div>
                    <label for="item_quantity_${itemCount}" class="block text-sm font-medium text-gray-700 mb-1">Quantity*</label>
                    <input type="number" id="item_quantity_${itemCount}" name="item_quantity[]" class="item-quantity w-full border-gray-300 rounded-md shadow-sm focus:border-apple-blue focus:ring focus:ring-apple-blue focus:ring-opacity-50" placeholder="1" min="1" value="1" step="0.01" required>
                </div>
                <div>
                    <label for="item_price_${itemCount}" class="block text-sm font-medium text-gray-700 mb-1">Unit Price (₹)*</label>
                    <input type="number" id="item_price_${itemCount}" name="item_price[]" class="item-price w-full border-gray-300 rounded-md shadow-sm focus:border-apple-blue focus:ring focus:ring-apple-blue focus:ring-opacity-50" placeholder="0.00" min="0" step="0.01" required>
                </div>
                <div class="md:col-span-3">
                    <label for="item_description_${itemCount}" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <input type="text" id="item_description_${itemCount}" name="item_description[]" class="w-full border-gray-300 rounded-md shadow-sm focus:border-apple-blue focus:ring focus:ring-apple-blue focus:ring-opacity-50" placeholder="Description of the service or product">
                </div>
                <div>
                    <label for="item_total_${itemCount}" class="block text-sm font-medium text-gray-700 mb-1">Total (₹)</label>
                    <input type="text" id="item_total_${itemCount}" name="item_total[]" class="item-total w-full bg-gray-100 border-gray-300 rounded-md shadow-sm" readonly>
                </div>
            </div>
        </div>
    `;
    
    invoiceItems.appendChild(newItem);
    
    // Attach change handlers for calculation
    const quantityInput = newItem.querySelector('.item-quantity');
    const priceInput = newItem.querySelector('.item-price');
    
    quantityInput.addEventListener('change', function() {
        calculateItemTotal(this);
        updateInvoiceTotals();
    });
    
    priceInput.addEventListener('change', function() {
        calculateItemTotal(this);
        updateInvoiceTotals();
    });
}

// Remove an invoice item field
function removeInvoiceItem(button) {
    const invoiceItem = button.closest('.invoice-item');
    invoiceItem.remove();
    updateInvoiceTotals();
}

// Calculate total for a single invoice item
function calculateItemTotal(input) {
    const invoiceItem = input.closest('.invoice-item');
    const quantity = parseFloat(invoiceItem.querySelector('.item-quantity').value) || 0;
    const price = parseFloat(invoiceItem.querySelector('.item-price').value) || 0;
    const total = quantity * price;
    
    invoiceItem.querySelector('.item-total').value = total.toFixed(2);
}

// Update all totals in the invoice
function updateInvoiceTotals() {
    // Calculate subtotal from all items
    let subtotal = 0;
    document.querySelectorAll('.item-total').forEach(function(totalInput) {
        subtotal += parseFloat(totalInput.value) || 0;
    });
    
    // Get tax rate and calculate tax amount
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
    const taxAmount = subtotal * (taxRate / 100);
    
    // Get discount amount
    const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
    
    // Calculate total
    const totalAmount = subtotal + taxAmount - discountAmount;
    
    // Update display and hidden fields
    document.getElementById('subtotal').textContent = '₹' + subtotal.toFixed(2);
    document.getElementById('subtotal_input').value = subtotal.toFixed(2);
    
    document.getElementById('tax_amount').textContent = '₹' + taxAmount.toFixed(2);
    document.getElementById('tax_amount_input').value = taxAmount.toFixed(2);
    
    document.getElementById('total_amount').textContent = '₹' + totalAmount.toFixed(2);
    document.getElementById('total_amount_input').value = totalAmount.toFixed(2);
}
