<?php
/**
 * Demo Login Handler
 * Creates PHP sessions for demo authentication
 */

session_start();

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid input']);
    exit;
}

// Demo credentials
$validCredentials = [
    [
        'email' => '<EMAIL>',
        'password' => 'admin123',
        'role' => 'admin',
        'name' => 'System Administrator',
        'id' => 1
    ],
    [
        'email' => '<EMAIL>',
        'password' => 'manager123',
        'role' => 'manager',
        'name' => 'Manager User',
        'id' => 2
    ],
    [
        'email' => '<EMAIL>',
        'password' => 'employee123',
        'role' => 'employee',
        'name' => 'Employee User',
        'id' => 3
    ]
];

// Find user
$user = null;
foreach ($validCredentials as $cred) {
    if ($cred['email'] === $input['email'] && $cred['password'] === $input['password']) {
        $user = $cred;
        break;
    }
}

if ($user) {
    // Create session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_name'] = $user['name'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_role'] = $user['role'];
    
    echo json_encode([
        'success' => true,
        'message' => 'Login successful',
        'user' => [
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'role' => $user['role']
        ]
    ]);
} else {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Invalid credentials']);
}
?>
