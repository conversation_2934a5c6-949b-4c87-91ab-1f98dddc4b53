/**
 * Sidebar Initialization Script
 * This script ensures the sidebar is properly initialized when the page loads
 */

// Run immediately when the script loads
(function() {
  // Force sidebar items to be visible on page load
  function initSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarItems = document.querySelectorAll('.sidebar-item-label');
    const sidebarLogo = document.getElementById('sidebar-logo');
    const sidebarLogoSmall = document.getElementById('sidebar-logo-small');
    
    // Check if sidebar is collapsed
    const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
    
    if (isCollapsed) {
      // If collapsed, hide items
      sidebarItems.forEach(item => {
        item.style.display = 'none';
        item.classList.add('hidden');
      });
      
      // Show small logo
      if (sidebarLogo && sidebarLogoSmall) {
        sidebarLogo.style.display = 'none';
        sidebarLogo.classList.add('hidden');
        sidebarLogoSmall.style.display = 'block';
        sidebarLogoSmall.classList.remove('hidden');
      }
    } else {
      // If expanded, show items
      sidebarItems.forEach(item => {
        item.style.display = 'inline-block';
        item.classList.remove('hidden');
      });
      
      // Show full logo
      if (sidebarLogo && sidebarLogoSmall) {
        sidebarLogo.style.display = 'block';
        sidebarLogo.classList.remove('hidden');
        sidebarLogoSmall.style.display = 'none';
        sidebarLogoSmall.classList.add('hidden');
      }
    }
  }
  
  // Run on DOMContentLoaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initSidebar);
  } else {
    // DOM already loaded, run now
    initSidebar();
  }
  
  // Also run after a short delay to ensure everything is loaded
  setTimeout(initSidebar, 100);
})();
