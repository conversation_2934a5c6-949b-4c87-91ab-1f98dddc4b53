<?php

/**
 * Enterprise ERP System - Sidebar Navigation
 * 
 * This file contains the main sidebar navigation with all ERP modules
 * organized by departments and functionality.
 */

// Get current page for active menu highlighting
$currentPage = basename($_SERVER['PHP_SELF']);
$currentDir = basename(dirname($_SERVER['PHP_SELF']));
?>

<!-- Sidebar -->
<aside id="sidebar" class="fixed top-16 left-0 z-20 w-64 h-screen transition-all duration-300 -translate-x-full bg-gray-50 border-r border-gray-200 lg:translate-x-0 lg:block sidebar-expanded">
    <div class="h-full px-3 py-4 overflow-y-auto sidebar-container">

        <!-- Dashboard -->
        <ul class="space-y-2 font-medium">
            <li>
                <a href="../dashboard/index.php"
                    class="flex items-center p-2 text-gray-900 rounded-lg hover:bg-gray-100 group <?php echo ($currentDir == 'dashboard') ? 'bg-primary text-white hover:bg-primary-dark' : ''; ?>"
                    title="Dashboard">
                    <i class="fas fa-tachometer-alt w-5 h-5 flex-shrink-0"></i>
                    <span class="ml-3 sidebar-text">Dashboard</span>
                </a>
            </li>
        </ul>

        <!-- Sales Module -->
        <div class="mt-6">
            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider sidebar-text">Sales</h3>
            <ul class="mt-2 space-y-1">
                <li>
                    <button type="button" class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg group hover:bg-gray-100"
                        onclick="toggleSubmenu('sales-submenu')" title="Sales">
                        <i class="fas fa-chart-line w-5 h-5 flex-shrink-0"></i>
                        <span class="flex-1 ml-3 text-left whitespace-nowrap sidebar-text">Sales</span>
                        <i class="fas fa-chevron-down w-3 h-3 sidebar-text"></i>
                    </button>
                    <ul id="sales-submenu" class="submenu hidden py-2 space-y-2">
                        <li>
                            <a href="../sales/customers.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'customers.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-users w-4 h-4"></i>
                                <span class="ml-2">Customers</span>
                            </a>
                        </li>
                        <li>
                            <a href="../sales/quotations.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'quotations.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-file-invoice w-4 h-4"></i>
                                <span class="ml-2">Quotations</span>
                            </a>
                        </li>
                        <li>
                            <a href="../sales/orders.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'orders.php' && $currentDir == 'sales') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-shopping-cart w-4 h-4"></i>
                                <span class="ml-2">Sales Orders</span>
                            </a>
                        </li>
                        <li>
                            <a href="../sales/invoices.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'invoices.php' && $currentDir == 'sales') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-receipt w-4 h-4"></i>
                                <span class="ml-2">Sales Invoices</span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- Purchase Module -->
        <div class="mt-6">
            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider sidebar-text">Purchase</h3>
            <ul class="mt-2 space-y-1">
                <li>
                    <button type="button" class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg group hover:bg-gray-100"
                        onclick="toggleSubmenu('purchase-submenu')" title="Purchase">
                        <i class="fas fa-shopping-bag w-5 h-5 flex-shrink-0"></i>
                        <span class="flex-1 ml-3 text-left whitespace-nowrap sidebar-text">Purchase</span>
                        <i class="fas fa-chevron-down w-3 h-3 sidebar-text"></i>
                    </button>
                    <ul id="purchase-submenu" class="submenu hidden py-2 space-y-2">
                        <li>
                            <a href="../purchase/vendors.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'vendors.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-truck w-4 h-4"></i>
                                <span class="ml-2">Vendors</span>
                            </a>
                        </li>
                        <li>
                            <a href="../purchase/requests.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'requests.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-clipboard-list w-4 h-4"></i>
                                <span class="ml-2">Purchase Requests</span>
                            </a>
                        </li>
                        <li>
                            <a href="../purchase/orders.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'orders.php' && $currentDir == 'purchase') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-file-contract w-4 h-4"></i>
                                <span class="ml-2">Purchase Orders</span>
                            </a>
                        </li>
                        <li>
                            <a href="../purchase/invoices.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'invoices.php' && $currentDir == 'purchase') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-file-invoice-dollar w-4 h-4"></i>
                                <span class="ml-2">Purchase Invoices</span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- Inventory Module -->
        <div class="mt-6">
            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider sidebar-text">Inventory</h3>
            <ul class="mt-2 space-y-1">
                <li>
                    <button type="button" class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg group hover:bg-gray-100"
                        onclick="toggleSubmenu('inventory-submenu')" title="Inventory">
                        <i class="fas fa-boxes w-5 h-5 flex-shrink-0"></i>
                        <span class="flex-1 ml-3 text-left whitespace-nowrap sidebar-text">Inventory</span>
                        <i class="fas fa-chevron-down w-3 h-3 sidebar-text"></i>
                    </button>
                    <ul id="inventory-submenu" class="submenu hidden py-2 space-y-2">
                        <li>
                            <a href="../inventory/products.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'products.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-cube w-4 h-4"></i>
                                <span class="ml-2">Products</span>
                            </a>
                        </li>
                        <li>
                            <a href="../inventory/stock.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'stock.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-warehouse w-4 h-4"></i>
                                <span class="ml-2">Stock Management</span>
                            </a>
                        </li>
                        <li>
                            <a href="../inventory/transfers.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'transfers.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-exchange-alt w-4 h-4"></i>
                                <span class="ml-2">Stock Transfers</span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- Finance Module -->
        <div class="mt-6">
            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider sidebar-text">Finance</h3>
            <ul class="mt-2 space-y-1">
                <li>
                    <button type="button" class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg group hover:bg-gray-100"
                        onclick="toggleSubmenu('finance-submenu')" title="Finance">
                        <i class="fas fa-calculator w-5 h-5 flex-shrink-0"></i>
                        <span class="flex-1 ml-3 text-left whitespace-nowrap sidebar-text">Finance</span>
                        <i class="fas fa-chevron-down w-3 h-3 sidebar-text"></i>
                    </button>
                    <ul id="finance-submenu" class="submenu hidden py-2 space-y-2">
                        <li>
                            <a href="../finance/accounts.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'accounts.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-book w-4 h-4"></i>
                                <span class="ml-2">Chart of Accounts</span>
                            </a>
                        </li>
                        <li>
                            <a href="../finance/journal.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'journal.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-journal-whills w-4 h-4"></i>
                                <span class="ml-2">Journal Entries</span>
                            </a>
                        </li>
                        <li>
                            <a href="../finance/reports.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'reports.php' && $currentDir == 'finance') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-chart-bar w-4 h-4"></i>
                                <span class="ml-2">Financial Reports</span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- HR Module -->
        <div class="mt-6">
            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider sidebar-text">Human Resources</h3>
            <ul class="mt-2 space-y-1">
                <li>
                    <button type="button" class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg group hover:bg-gray-100"
                        onclick="toggleSubmenu('hr-submenu')" title="HR">
                        <i class="fas fa-user-tie w-5 h-5 flex-shrink-0"></i>
                        <span class="flex-1 ml-3 text-left whitespace-nowrap sidebar-text">HR</span>
                        <i class="fas fa-chevron-down w-3 h-3 sidebar-text"></i>
                    </button>
                    <ul id="hr-submenu" class="submenu hidden py-2 space-y-2">
                        <li>
                            <a href="../hr/employees.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'employees.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-users w-4 h-4"></i>
                                <span class="ml-2">Employees</span>
                            </a>
                        </li>
                        <li>
                            <a href="../hr/attendance.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'attendance.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-clock w-4 h-4"></i>
                                <span class="ml-2">Attendance</span>
                            </a>
                        </li>
                        <li>
                            <a href="../hr/payroll.php"
                                class="flex items-center w-full p-2 text-gray-900 transition duration-75 rounded-lg pl-11 group hover:bg-gray-100 <?php echo ($currentPage == 'payroll.php') ? 'bg-gray-100' : ''; ?>">
                                <i class="fas fa-money-check-alt w-4 h-4"></i>
                                <span class="ml-2">Payroll</span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- Reports -->
        <div class="mt-6">
            <ul class="space-y-2 font-medium">
                <li>
                    <a href="../reports/index.php"
                        class="flex items-center p-2 text-gray-900 rounded-lg hover:bg-gray-100 group <?php echo ($currentDir == 'reports') ? 'bg-primary text-white hover:bg-primary-dark' : ''; ?>"
                        title="Reports & Analytics">
                        <i class="fas fa-chart-pie w-5 h-5 flex-shrink-0"></i>
                        <span class="ml-3 sidebar-text">Reports & Analytics</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Settings -->
        <div class="mt-6">
            <ul class="space-y-2 font-medium">
                <li>
                    <a href="../settings/index.php"
                        class="flex items-center p-2 text-gray-900 rounded-lg hover:bg-gray-100 group <?php echo ($currentDir == 'settings') ? 'bg-primary text-white hover:bg-primary-dark' : ''; ?>"
                        title="Settings">
                        <i class="fas fa-cog w-5 h-5 flex-shrink-0"></i>
                        <span class="ml-3 sidebar-text">Settings</span>
                    </a>
                </li>
            </ul>
        </div>

    </div>
</aside>