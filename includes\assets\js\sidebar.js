document.addEventListener("DOMContentLoaded", function () {
  // Toggle sidebar collapse
  const sidebarToggle = document.getElementById("sidebar-toggle");
  const sidebarToggleContainer = document.getElementById(
    "sidebar-toggle-container"
  );
  const sidebar = document.getElementById("sidebar");
  const mainContent = document.getElementById("main-content");
  const sidebarItems = document.querySelectorAll(".sidebar-item-label");
  const sidebarLogo = document.getElementById("sidebar-logo");
  const sidebarLogoSmall = document.getElementById("sidebar-logo-small");

  console.log("Sidebar elements check:", {
    sidebarToggle: !!sidebarToggle,
    sidebar: !!sidebar,
    mainContent: !!mainContent,
  });

  if (sidebarToggle && sidebar && mainContent) {
    console.log("Sidebar toggle found, adding event listener");
    sidebarToggle.addEventListener("click", function () {
      console.log("Sidebar toggle clicked");
      sidebar.classList.toggle("sidebar-collapsed");
      mainContent.classList.toggle("content-expanded");

      // Update toggle button position
      if (sidebar.classList.contains("sidebar-collapsed")) {
        sidebarToggleContainer.style.left = "80px";
      } else {
        sidebarToggleContainer.style.left = "260px";
      }

      // Toggle visibility of text labels and hide submenus when collapsed
      if (sidebar.classList.contains("sidebar-collapsed")) {
        // When collapsing
        // Hide all text labels
        sidebarItems.forEach((item) => {
          item.classList.add("hidden");
          item.style.display = "none";
          item.style.width = "0";
          item.style.height = "0";
          item.style.overflow = "hidden";
        });

        // Center all icons
        const sidebarItemsContainer =
          document.querySelectorAll(".sidebar-item");
        sidebarItemsContainer.forEach((item) => {
          item.style.justifyContent = "center";
          item.style.textAlign = "center";
          item.style.padding = "0.75rem";
        });

        // Hide all submenus
        const submenus = document.querySelectorAll(".submenu");
        const chevrons = document.querySelectorAll(
          ".submenu-toggle svg:last-child"
        );

        submenus.forEach((menu) => {
          menu.classList.add("hidden");
          menu.style.display = "none";
        });

        chevrons.forEach((chevron) => {
          chevron.classList.remove("rotate-90");
          chevron.style.display = "none";
        });

        // Show small logo
        if (sidebarLogo && sidebarLogoSmall) {
          sidebarLogo.classList.add("hidden");
          sidebarLogo.style.display = "none";
          sidebarLogoSmall.classList.remove("hidden");
          sidebarLogoSmall.style.display = "block";
        }
      } else {
        // When expanding
        // Show all text labels
        sidebarItems.forEach((item) => {
          item.classList.remove("hidden");
          item.style.display = "inline-block";
          item.style.width = "auto";
          item.style.height = "auto";
          item.style.overflow = "visible";
        });

        // Reset icon containers
        const sidebarItemsContainer =
          document.querySelectorAll(".sidebar-item");
        sidebarItemsContainer.forEach((item) => {
          item.style.justifyContent = "flex-start";
          item.style.textAlign = "left";
          item.style.padding = "0.75rem 1rem";
        });

        // Show chevrons
        const chevrons = document.querySelectorAll(
          ".submenu-toggle svg:last-child"
        );
        chevrons.forEach((chevron) => {
          chevron.style.display = "block";
        });

        // Show logo
        if (sidebarLogo && sidebarLogoSmall) {
          sidebarLogo.classList.remove("hidden");
          sidebarLogo.style.display = "block";
          sidebarLogoSmall.classList.add("hidden");
          sidebarLogoSmall.style.display = "none";
        }

        // Restore active submenu if any
        const activeSubmenuToggle = document.querySelector(
          ".submenu-toggle.active"
        );
        if (activeSubmenuToggle) {
          const parentLi = activeSubmenuToggle.closest("li");
          const submenu = parentLi.querySelector(".submenu");
          const chevron = activeSubmenuToggle.querySelector("svg:last-child");

          if (submenu) {
            submenu.classList.remove("hidden");
          }

          if (chevron) {
            chevron.classList.add("rotate-90");
          }
        }
      }

      // Store the sidebar state in localStorage
      const isCollapsed = sidebar.classList.contains("sidebar-collapsed");
      localStorage.setItem("sidebar-collapsed", isCollapsed);
    });

    // Check if sidebar was collapsed previously
    const wasCollapsed = localStorage.getItem("sidebar-collapsed") === "true";
    if (wasCollapsed) {
      sidebar.classList.add("sidebar-collapsed");
      mainContent.classList.add("content-expanded");

      // Update toggle button position
      sidebarToggleContainer.style.left = "80px";

      // Hide all text labels
      sidebarItems.forEach((item) => {
        item.classList.add("hidden");
        item.style.display = "none";
      });

      // Hide all submenus
      const submenus = document.querySelectorAll(".submenu");
      submenus.forEach((menu) => {
        menu.classList.add("hidden");
        menu.style.display = "none";
      });

      // Show small logo
      if (sidebarLogo && sidebarLogoSmall) {
        sidebarLogo.classList.add("hidden");
        sidebarLogo.style.display = "none";
        sidebarLogoSmall.classList.remove("hidden");
        sidebarLogoSmall.style.display = "block";
      }
    } else {
      // Set initial position for toggle button
      sidebarToggleContainer.style.left = "260px";

      // Make sure all text labels are visible
      sidebarItems.forEach((item) => {
        item.classList.remove("hidden");
        item.style.display = "inline-block";
      });

      // Show full logo
      if (sidebarLogo && sidebarLogoSmall) {
        sidebarLogo.classList.remove("hidden");
        sidebarLogo.style.display = "block";
        sidebarLogoSmall.classList.add("hidden");
        sidebarLogoSmall.style.display = "none";
      }
    }
  }

  // Handle submenu toggles
  const submenuToggles = document.querySelectorAll(".submenu-toggle");
  const menuItems = document.querySelectorAll(".sidebar-menu > li");

  // Add hover functionality for collapsed sidebar
  if (sidebar) {
    menuItems.forEach((item) => {
      const hasSubmenu = item.querySelector(".submenu") !== null;

      if (hasSubmenu) {
        // For touch devices, we need to handle touch events differently
        item.addEventListener("mouseenter", function () {
          if (sidebar.classList.contains("sidebar-collapsed")) {
            const submenu = this.querySelector(".submenu");
            if (submenu) {
              // Hide all other submenus first
              document
                .querySelectorAll(".sidebar-menu > li > .submenu")
                .forEach((menu) => {
                  if (menu !== submenu) {
                    menu.classList.add("hidden");
                  }
                });

              // Show this submenu
              submenu.classList.remove("hidden");
            }
          }
        });

        item.addEventListener("mouseleave", function () {
          if (sidebar.classList.contains("sidebar-collapsed")) {
            const submenu = this.querySelector(".submenu");
            if (submenu) {
              submenu.classList.add("hidden");
            }
          }
        });
      }
    });
  }

  // Handle click for submenu toggles
  console.log("Found submenu toggles:", submenuToggles.length);

  submenuToggles.forEach((toggle) => {
    toggle.addEventListener("click", function (e) {
      console.log("Submenu toggle clicked");
      e.preventDefault();

      // Don't toggle submenu if sidebar is collapsed
      if (sidebar.classList.contains("sidebar-collapsed")) {
        console.log("Sidebar is collapsed, not toggling submenu");
        return;
      }

      // Get the parent li element
      const parentLi = this.closest("li");
      console.log("Parent li found:", !!parentLi);

      // Toggle the submenu
      const submenu = parentLi.querySelector(".submenu");
      console.log("Submenu found:", !!submenu);

      if (submenu) {
        const isHidden = submenu.classList.contains("hidden");
        console.log("Submenu is hidden:", isHidden);
        submenu.classList.toggle("hidden");
      }

      // Toggle the chevron icon
      const chevron = this.querySelector("svg:last-child");
      console.log("Chevron found:", !!chevron);

      if (chevron) {
        chevron.classList.toggle("rotate-90");
      }

      // Close other submenus
      const otherLis = document.querySelectorAll(".sidebar-menu > li");

      otherLis.forEach((li) => {
        if (li !== parentLi) {
          const otherSubmenu = li.querySelector(".submenu");
          const otherChevron = li.querySelector(
            ".submenu-toggle svg:last-child"
          );

          if (otherSubmenu && !otherSubmenu.classList.contains("hidden")) {
            otherSubmenu.classList.add("hidden");
            if (otherChevron) {
              otherChevron.classList.remove("rotate-90");
            }
          }
        }
      });
    });
  });

  // Highlight active menu item based on current page
  const currentPath = window.location.pathname;
  const menuLinks = document.querySelectorAll(".sidebar-menu a");

  menuLinks.forEach((link) => {
    const linkPath = link.getAttribute("href");
    if (linkPath && linkPath !== "#" && currentPath.includes(linkPath)) {
      // Add active class to the link
      if (link.classList.contains("submenu-toggle")) {
        // If it's a submenu toggle, add active class and open submenu
        link.classList.add("active");
        const parentLi = link.closest("li");
        const submenu = parentLi.querySelector(".submenu");
        const chevron = link.querySelector("svg:last-child");

        if (submenu && !sidebar.classList.contains("sidebar-collapsed")) {
          submenu.classList.remove("hidden");
        }

        if (chevron && !sidebar.classList.contains("sidebar-collapsed")) {
          chevron.classList.add("rotate-90");
        }
      } else if (link.closest(".submenu")) {
        // If it's a submenu item
        link.classList.add("active-menu-item");

        // Open parent submenu if sidebar is not collapsed
        if (!sidebar.classList.contains("sidebar-collapsed")) {
          const parentSubmenu = link.closest(".submenu");
          if (parentSubmenu) {
            parentSubmenu.classList.remove("hidden");
            const parentLi = parentSubmenu.closest("li");
            const parentLink = parentLi.querySelector(".submenu-toggle");
            const chevron = parentLink.querySelector("svg:last-child");

            parentLink.classList.add("active");

            if (chevron) {
              chevron.classList.add("rotate-90");
            }
          }
        }
      } else {
        // Regular menu item
        link.classList.add("active-menu-item");
      }
    }
  });
});
