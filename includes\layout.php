<?php

/**
 * Layout Template
 *
 * This file contains the common layout elements (sidebar, navbar) that are used across all pages.
 * Include this file at the beginning of each page to maintain consistent layout.
 *
 * Usage:
 * 1. Include this file at the top of your page
 * 2. Set $pageTitle variable before including this file
 * 3. Start your page content after the include
 *
 * Example:
 * <?php
 *   $pageTitle = "Dashboard";
 *   include 'layout.php';
 * ?>
 * <div>Your page content here</div>
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in, if not redirect to login page
if (!isset($_SESSION['user_id']) && basename($_SERVER['PHP_SELF']) !== 'index.php') {
    header("Location: index.php");
    exit;
}

// Get user information from session
$userName = $_SESSION['user_name'] ?? 'Admin User';
$userEmail = $_SESSION['user_email'] ?? '<EMAIL>';

// Handle avatar - if it's stored as a path, use it; otherwise generate one
$userAvatar = !empty($_SESSION['user_avatar'])
    ? $_SESSION['user_avatar']
    : 'https://ui-avatars.com/api/?name=' . urlencode($userName) . '&background=0071e3&color=fff';

// Set default page title if not provided
if (!isset($pageTitle)) {
    $pageTitle = "Alyanka CRM";
}

// Set active page for menu highlighting
if (!isset($activePage)) {
    $activePage = basename($_SERVER['PHP_SELF']);
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Alyanka CRM</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'apple-blue': '#0071e3',
                        'apple-gray': '#f5f5f7',
                        'apple-dark': '#1d1d1f'
                    },
                    fontFamily: {
                        'sans': ['-apple-system', 'BlinkMacSystemFont', 'San Francisco', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'sans-serif']
                    },
                    boxShadow: {
                        'apple': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="assets/css/style.css">
    <?php if (isset($extraStyles)) echo $extraStyles; ?>
    <?php if (isset($customCSS)) echo $customCSS; ?>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="assets/js/sidebar-init.js"></script>
    <script src="assets/js/script.js"></script>
    <script src="assets/js/sidebar.js"></script>
    <script src="assets/js/submenu-fix.js"></script>

    <!-- Inline script for immediate submenu fix -->
    <script>
        // Direct submenu toggle fix
        document.addEventListener('DOMContentLoaded', function() {
            // Apply after a short delay to ensure DOM is fully loaded
            setTimeout(function() {
                // Get all submenu toggles
                const submenuToggles = document.querySelectorAll('.submenu-toggle');

                // Add direct click handlers
                submenuToggles.forEach(function(toggle) {
                    toggle.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Get the submenu
                        const submenu = this.nextElementSibling;

                        if (submenu) {
                            // Force toggle
                            if (submenu.style.display === 'none' || submenu.classList.contains('hidden')) {
                                submenu.style.display = 'block';
                                submenu.classList.remove('hidden');

                                // Rotate chevron
                                const chevron = this.querySelector('svg:last-child');
                                if (chevron) {
                                    chevron.style.transform = 'rotate(90deg)';
                                }
                            } else {
                                submenu.style.display = 'none';
                                submenu.classList.add('hidden');

                                // Reset chevron
                                const chevron = this.querySelector('svg:last-child');
                                if (chevron) {
                                    chevron.style.transform = 'rotate(0deg)';
                                }
                            }

                            // Close other submenus
                            submenuToggles.forEach(function(otherToggle) {
                                if (otherToggle !== toggle) {
                                    const otherSubmenu = otherToggle.nextElementSibling;
                                    if (otherSubmenu) {
                                        otherSubmenu.style.display = 'none';
                                        otherSubmenu.classList.add('hidden');

                                        // Reset chevron
                                        const otherChevron = otherToggle.querySelector('svg:last-child');
                                        if (otherChevron) {
                                            otherChevron.style.transform = 'rotate(0deg)';
                                        }
                                    }
                                }
                            });
                        }

                        return false;
                    };
                });
            }, 100);
        });
    </script>
</head>

<body class="bg-apple-gray min-h-screen">
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebar-overlay"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="sidebar bg-white h-screen fixed top-0 left-0 overflow-y-auto shadow-md z-30">
        <!-- Sidebar Header -->
        <div class="flex items-center justify-center p-4 border-b">
            <div class="flex items-center">
                <!-- <span id="sidebar-logo" class="text-xl font-semibold text-apple-dark">Alyanka CRM</span> -->
                <span id="sidebar-logo-small" class="text-xl font-semibold text-apple-dark hidden">A</span>
            </div>
        </div>

        <!-- Sidebar Toggle Button (Fixed Position) -->
        <div id="sidebar-toggle-container" class="fixed top-4 left-[260px] z-40 transition-all duration-300">
            <button type="button" id="sidebar-toggle" class="bg-white rounded-full shadow-md p-2 flex items-center justify-center hover:bg-gray-100 cursor-pointer">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>

        <!-- Sidebar Menu -->
        <nav class="p-4">
            <ul class="sidebar-menu space-y-2">
                <!-- Dashboard with submenu -->
                <li>
                    <a href="#" class="sidebar-item submenu-toggle <?php echo (strpos($activePage, 'dashboard') !== false) ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                        <span class="sidebar-item-label">Dashboard</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <ul class="submenu <?php echo (strpos($activePage, 'dashboard') !== false) ? '' : 'hidden'; ?>">
                        <li><a href="dashboard.php" class="submenu-item <?php echo ($activePage == 'dashboard.php') ? 'active-menu-item' : ''; ?>">Overview</a></li>
                        <li><a href="dashboard-analytics.php" class="submenu-item <?php echo ($activePage == 'dashboard-analytics.php') ? 'active-menu-item' : ''; ?>">Analytics</a></li>
                        <li><a href="dashboard-reports.php" class="submenu-item <?php echo ($activePage == 'dashboard-reports.php') ? 'active-menu-item' : ''; ?>">Reports</a></li>
                    </ul>
                </li>

                <!-- Leads with submenu -->
                <li>
                    <a href="#" class="sidebar-item submenu-toggle <?php echo (strpos($activePage, 'lead') !== false) ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span class="sidebar-item-label">Leads</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <ul class="submenu <?php echo (strpos($activePage, 'lead') !== false) ? '' : 'hidden'; ?>">
                        <li><a href="leads.php" class="submenu-item <?php echo ($activePage == 'leads.php') ? 'active-menu-item' : ''; ?>">All Leads</a></li>
                        <li><a href="lead-categories.php" class="submenu-item <?php echo ($activePage == 'lead-categories.php') ? 'active-menu-item' : ''; ?>">Lead Categories</a></li>
                    </ul>
                </li>

                <!-- Projects with submenu -->
                <li>
                    <a href="#" class="sidebar-item submenu-toggle <?php echo (strpos($activePage, 'project') !== false) ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        <span class="sidebar-item-label">Projects</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <ul class="submenu <?php echo (strpos($activePage, 'project') !== false) ? '' : 'hidden'; ?>">
                        <li><a href="projects.php" class="submenu-item <?php echo ($activePage == 'projects.php') ? 'active-menu-item' : ''; ?>">All Projects</a></li>
                        <li><a href="project-add.php" class="submenu-item <?php echo ($activePage == 'project-add.php') ? 'active-menu-item' : ''; ?>">Add Project</a></li>
                        <li><a href="project-categories.php" class="submenu-item <?php echo ($activePage == 'project-categories.php') ? 'active-menu-item' : ''; ?>">Project Categories</a></li>
                    </ul>
                </li>

                <!-- Portfolio with submenu -->
                <li>
                    <a href="#" class="sidebar-item submenu-toggle <?php echo (strpos($activePage, 'portfolio') !== false) ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span class="sidebar-item-label">Portfolio</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <ul class="submenu <?php echo (strpos($activePage, 'portfolio') !== false) ? '' : 'hidden'; ?>">
                        <li><a href="portfolio-website.php" class="submenu-item <?php echo ($activePage == 'portfolio-website.php') ? 'active-menu-item' : ''; ?>">Website</a></li>
                        <li><a href="portfolio-application.php" class="submenu-item <?php echo ($activePage == 'portfolio-application.php') ? 'active-menu-item' : ''; ?>">Application</a></li>
                        <li><a href="portfolio-software.php" class="submenu-item <?php echo ($activePage == 'portfolio-software.php') ? 'active-menu-item' : ''; ?>">Software</a></li>
                        <li><a href="portfolio-social-media.php" class="submenu-item <?php echo ($activePage == 'portfolio-social-media.php') ? 'active-menu-item' : ''; ?>">Social Media</a></li>
                        <li><a href="portfolio-blog.php" class="submenu-item <?php echo ($activePage == 'portfolio-blog.php') ? 'active-menu-item' : ''; ?>">Blog</a></li>
                        <li><a href="portfolio-other.php" class="submenu-item <?php echo ($activePage == 'portfolio-other.php') ? 'active-menu-item' : ''; ?>">Other</a></li>
                    </ul>
                </li>

                <!-- Financial with submenu -->
                <li>
                    <a href="#" class="sidebar-item submenu-toggle <?php echo (strpos($activePage, 'financial') !== false) ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span class="sidebar-item-label">Financial</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <ul class="submenu <?php echo (strpos($activePage, 'financial') !== false || strpos($activePage, 'scope-of-work') !== false) ? '' : 'hidden'; ?>">
                        <li><a href="financial-overview.php" class="submenu-item <?php echo ($activePage == 'financial-overview.php') ? 'active-menu-item' : ''; ?>">Overview</a></li>
                        <li><a href="financial-invoices.php" class="submenu-item <?php echo ($activePage == 'financial-invoices.php') ? 'active-menu-item' : ''; ?>">Invoices</a></li>
                        <li><a href="scope-of-work.php" class="submenu-item <?php echo ($activePage == 'scope-of-work.php' || strpos($activePage, 'scope') !== false) ? 'active-menu-item' : ''; ?>">Scope of Work</a></li>
                        <li><a href="financial-expenses.php" class="submenu-item <?php echo ($activePage == 'financial-expenses.php') ? 'active-menu-item' : ''; ?>">Expenses</a></li>
                        <li><a href="financial-categories.php" class="submenu-item <?php echo ($activePage == 'financial-categories.php') ? 'active-menu-item' : ''; ?>">Expense Categories</a></li>
                        <li><a href="financial-transactions.php" class="submenu-item <?php echo ($activePage == 'financial-transactions.php') ? 'active-menu-item' : ''; ?>">Transactions</a></li>
                    </ul>
                </li>

                <!-- Pricing with submenu -->
                <li>
                    <a href="#" class="sidebar-item submenu-toggle <?php echo (strpos($activePage, 'pricing') !== false) ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                        <span class="sidebar-item-label">Pricing</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <ul class="submenu <?php echo (strpos($activePage, 'pricing') !== false) ? '' : 'hidden'; ?>">
                        <li><a href="pricing-packages.php" class="submenu-item <?php echo ($activePage == 'pricing-packages.php') ? 'active-menu-item' : ''; ?>">Packages</a></li>
                        <li><a href="pricing-services.php" class="submenu-item <?php echo ($activePage == 'pricing-services.php') ? 'active-menu-item' : ''; ?>">Services</a></li>
                        <li><a href="manage-services.php" class="submenu-item <?php echo ($activePage == 'manage-services.php') ? 'active-menu-item' : ''; ?>">Manage Services</a></li>
                        <li><a href="pricing-calculator.php" class="submenu-item <?php echo ($activePage == 'pricing-calculator.php') ? 'active-menu-item' : ''; ?>">Calculator</a></li>
                    </ul>
                </li>

                <!-- Proposals with submenu -->
                <li>
                    <a href="#" class="sidebar-item submenu-toggle <?php echo (strpos($activePage, 'proposal') !== false) ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span class="sidebar-item-label">Proposals</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <ul class="submenu <?php echo (strpos($activePage, 'proposal') !== false) ? '' : 'hidden'; ?>">
                        <li><a href="proposal-templates.php" class="submenu-item <?php echo ($activePage == 'proposal-templates.php') ? 'active-menu-item' : ''; ?>">Templates</a></li>
                        <li><a href="proposal-create.php" class="submenu-item <?php echo ($activePage == 'proposal-create.php') ? 'active-menu-item' : ''; ?>">Create New</a></li>
                        <li><a href="proposal-sent.php" class="submenu-item <?php echo ($activePage == 'proposal-sent.php') ? 'active-menu-item' : ''; ?>">Sent Proposals</a></li>
                    </ul>
                </li>

                <!-- Mail with submenu -->
                <li>
                    <a href="#" class="sidebar-item submenu-toggle <?php echo (strpos($activePage, 'mail') !== false) ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <span class="sidebar-item-label">Mail</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <ul class="submenu <?php echo (strpos($activePage, 'mail') !== false) ? '' : 'hidden'; ?>">
                        <li><a href="mail-inbox.php" class="submenu-item <?php echo ($activePage == 'mail-inbox.php') ? 'active-menu-item' : ''; ?>">Inbox</a></li>
                        <li><a href="mail-compose.php" class="submenu-item <?php echo ($activePage == 'mail-compose.php') ? 'active-menu-item' : ''; ?>">Compose</a></li>
                        <li><a href="mail-templates.php" class="submenu-item <?php echo ($activePage == 'mail-templates.php') ? 'active-menu-item' : ''; ?>">Templates</a></li>
                        <li><a href="mail-template-builder.php" class="submenu-item <?php echo ($activePage == 'mail-template-builder.php') ? 'active-menu-item' : ''; ?>">Template Builder</a></li>
                        <li><a href="mail-campaigns.php" class="submenu-item <?php echo ($activePage == 'mail-campaigns.php') ? 'active-menu-item' : ''; ?>">Campaigns</a></li>
                    </ul>
                </li>

                <!-- Client Portal with submenu -->
                <li>
                    <a href="#" class="sidebar-item submenu-toggle <?php echo (strpos($activePage, 'client-portal') !== false) ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                        </svg>
                        <span class="sidebar-item-label">Client Portal</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <ul class="submenu <?php echo (strpos($activePage, 'client-portal') !== false) ? '' : 'hidden'; ?>">
                        <li><a href="client-portal-dashboard.php" class="submenu-item <?php echo ($activePage == 'client-portal-dashboard.php') ? 'active-menu-item' : ''; ?>">Portal Dashboard</a></li>
                        <li><a href="client-portal-documents.php" class="submenu-item <?php echo ($activePage == 'client-portal-documents.php') ? 'active-menu-item' : ''; ?>">Documents</a></li>
                        <li><a href="client-portal-tickets.php" class="submenu-item <?php echo ($activePage == 'client-portal-tickets.php') ? 'active-menu-item' : ''; ?>">Support Tickets</a></li>
                        <li><a href="client-portal-settings.php" class="submenu-item <?php echo ($activePage == 'client-portal-settings.php') ? 'active-menu-item' : ''; ?>">Portal Settings</a></li>
                    </ul>
                </li>

                <!-- Task Management with submenu -->
                <li>
                    <a href="#" class="sidebar-item submenu-toggle <?php echo (strpos($activePage, 'task') !== false) ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                        </svg>
                        <span class="sidebar-item-label">Task Management</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <ul class="submenu <?php echo (strpos($activePage, 'task') !== false) ? '' : 'hidden'; ?>">
                        <li><a href="task-kanban.php" class="submenu-item <?php echo ($activePage == 'task-kanban.php') ? 'active-menu-item' : ''; ?>">Kanban Board</a></li>
                        <li><a href="task-time-tracking.php" class="submenu-item <?php echo ($activePage == 'task-time-tracking.php') ? 'active-menu-item' : ''; ?>">Time Tracking</a></li>
                        <li><a href="task-team-management.php" class="submenu-item <?php echo ($activePage == 'task-team-management.php') ? 'active-menu-item' : ''; ?>">Team Management</a></li>
                        <li><a href="task-resource-allocation.php" class="submenu-item <?php echo ($activePage == 'task-resource-allocation.php') ? 'active-menu-item' : ''; ?>">Resource Allocation</a></li>
                    </ul>
                </li>

                <!-- Reports with submenu -->
                <li>
                    <a href="#" class="sidebar-item submenu-toggle <?php echo (strpos($activePage, 'report') !== false) ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        <span class="sidebar-item-label">Reports</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                    <ul class="submenu <?php echo (strpos($activePage, 'report') !== false) ? '' : 'hidden'; ?>">
                        <li><a href="report-sales.php" class="submenu-item <?php echo ($activePage == 'report-sales.php') ? 'active-menu-item' : ''; ?>">Sales Reports</a></li>
                        <li><a href="report-customers.php" class="submenu-item <?php echo ($activePage == 'report-customers.php') ? 'active-menu-item' : ''; ?>">Customer Reports</a></li>
                        <li><a href="report-projects.php" class="submenu-item <?php echo ($activePage == 'report-projects.php') ? 'active-menu-item' : ''; ?>">Project Reports</a></li>
                        <li><a href="report-custom.php" class="submenu-item <?php echo ($activePage == 'report-custom.php') ? 'active-menu-item' : ''; ?>">Custom Reports</a></li>
                        <li><a href="report-scheduled.php" class="submenu-item <?php echo ($activePage == 'report-scheduled.php') ? 'active-menu-item' : ''; ?>">Scheduled Reports</a></li>
                    </ul>
                </li>

                <!-- Settings -->
                <li>
                    <a href="settings.php" class="sidebar-item <?php echo ($activePage == 'settings.php') ? 'active' : ''; ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span class="sidebar-item-label">Settings</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Top Navigation -->
    <nav class="bg-white shadow-sm transition-all duration-300" id="top-nav">
        <div class="max-w-full px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Mobile menu button (only visible on small screens) -->
                    <button type="button" id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-apple-blue sm:hidden">
                        <span class="sr-only">Open main menu</span>
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                    <span class="ml-2 text-lg font-medium text-apple-dark sm:hidden">Alyanka CRM</span>
                </div>

                <div class="flex items-center">
                    <!-- Notification dropdown -->
                    <div class="ml-3 relative">
                        <button id="notification-button" class="p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-apple-blue">
                            <span class="sr-only">View notifications</span>
                            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                        </button>

                        <!-- Notification dropdown panel -->
                        <div id="notification-dropdown" class="hidden origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 focus:outline-none z-50">
                            <div class="px-4 py-3">
                                <p class="text-sm font-medium text-gray-900">Notifications</p>
                            </div>
                            <div class="py-1">
                                <a href="#" class="flex px-4 py-3 hover:bg-gray-50">
                                    <div class="flex-shrink-0">
                                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=John+Doe&background=0071e3&color=fff" alt="User">
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">John Doe added a new customer</p>
                                        <p class="text-xs text-gray-500">2 hours ago</p>
                                    </div>
                                </a>
                                <a href="#" class="flex px-4 py-3 hover:bg-gray-50">
                                    <div class="flex-shrink-0">
                                        <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Sarah+Smith&background=0071e3&color=fff" alt="User">
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">Sarah Smith completed a project</p>
                                        <p class="text-xs text-gray-500">5 hours ago</p>
                                    </div>
                                </a>
                            </div>
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-center text-apple-blue">View all notifications</a>
                            </div>
                        </div>
                    </div>

                    <!-- Profile dropdown -->
                    <div class="ml-3 relative">
                        <div>
                            <button type="button" class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-apple-blue" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                                <span class="sr-only">Open user menu</span>
                                <img class="h-8 w-8 rounded-full" src="<?php echo $userAvatar; ?>" alt="User profile">
                            </button>
                        </div>

                        <!-- User dropdown menu -->
                        <div id="user-dropdown" class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 focus:outline-none z-50">
                            <div class="px-4 py-3">
                                <p class="text-sm font-medium text-gray-900"><?php echo $userName; ?></p>
                                <p class="text-sm text-gray-500 truncate"><?php echo $userEmail; ?></p>
                            </div>
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Your Profile</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Settings</a>
                            </div>
                            <div class="py-1">
                                <a href="../backend/api/logout.php" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-50">Sign out</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content" class="py-6 px-4 sm:px-6 lg:px-8">
        <!-- Page content will be inserted here -->
        <?php if (isset($pageHeader)): ?>
            <div class="mb-8">
                <h1 class="text-2xl md:text-3xl font-semibold text-apple-dark"><?php echo $pageHeader; ?></h1>
                <?php if (isset($pageDescription)): ?>
                    <p class="mt-2 text-gray-500"><?php echo $pageDescription; ?></p>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- Start of page content -->