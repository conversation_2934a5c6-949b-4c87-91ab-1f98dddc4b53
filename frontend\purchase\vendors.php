<?php
/**
 * Enterprise ERP System - Vendor Management
 * 
 * This page handles vendor/supplier listing, creation, and management
 */

// Page configuration
$pageTitle = "Vendor Management";
$pageHeader = "Vendors";
$pageDescription = "Manage your supplier database and vendor relationships.";

$breadcrumbs = [
    ['title' => 'Dashboard', 'url' => '../dashboard/index.php'],
    ['title' => 'Purchase'],
    ['title' => 'Vendors']
];

// Include main layout
include_once '../layouts/main.php';
?>

<!-- Page Actions -->
<div class="flex justify-between items-center mb-6">
    <div class="flex space-x-3">
        <button class="btn-primary">
            <i class="fas fa-plus mr-2"></i>Add Vendor
        </button>
        <button class="btn-secondary">
            <i class="fas fa-upload mr-2"></i>Import
        </button>
        <button class="btn-secondary">
            <i class="fas fa-download mr-2"></i>Export
        </button>
    </div>
    
    <div class="flex space-x-3">
        <div class="relative">
            <input type="text" placeholder="Search vendors..." 
                   class="form-input w-64 pl-10">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
        </div>
        <select class="form-select">
            <option>All Types</option>
            <option>Goods</option>
            <option>Services</option>
            <option>Both</option>
        </select>
    </div>
</div>

<!-- Vendor Stats -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="card">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                <i class="fas fa-truck text-blue-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500">Total Vendors</p>
                <p class="text-xl font-bold text-gray-900">456</p>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                <i class="fas fa-check-circle text-green-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500">Active Vendors</p>
                <p class="text-xl font-bold text-gray-900">398</p>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                <i class="fas fa-star text-purple-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500">Preferred Vendors</p>
                <p class="text-xl font-bold text-gray-900">45</p>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-orange-100 rounded-md flex items-center justify-center">
                <i class="fas fa-plus-circle text-orange-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500">This Month</p>
                <p class="text-xl font-bold text-gray-900">12</p>
            </div>
        </div>
    </div>
</div>

<!-- Vendor Table -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Vendor List</h3>
    </div>
    
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>Vendor ID</th>
                    <th>Company Name</th>
                    <th>Contact Person</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Location</th>
                    <th>Type</th>
                    <th>Total Orders</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="font-medium">#VEN-001</td>
                    <td>
                        <div>
                            <div class="font-medium">ABC Suppliers Ltd</div>
                            <div class="text-sm text-gray-500">Raw Materials</div>
                        </div>
                    </td>
                    <td>Rajesh Kumar</td>
                    <td><EMAIL></td>
                    <td>+91-9876543210</td>
                    <td>Mumbai, Maharashtra</td>
                    <td><span class="badge badge-info">Goods</span></td>
                    <td>28</td>
                    <td><span class="badge badge-success">Active</span></td>
                    <td>
                        <div class="flex space-x-2">
                            <button class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td class="font-medium">#VEN-002</td>
                    <td>
                        <div>
                            <div class="font-medium">Tech Services Pro</div>
                            <div class="text-sm text-gray-500">IT Services</div>
                        </div>
                    </td>
                    <td>Priya Sharma</td>
                    <td><EMAIL></td>
                    <td>+91-9876543211</td>
                    <td>Bangalore, Karnataka</td>
                    <td><span class="badge badge-warning">Services</span></td>
                    <td>15</td>
                    <td><span class="badge badge-success">Active</span></td>
                    <td>
                        <div class="flex space-x-2">
                            <button class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td class="font-medium">#VEN-003</td>
                    <td>
                        <div>
                            <div class="font-medium">Global Manufacturing</div>
                            <div class="text-sm text-gray-500">Equipment & Parts</div>
                        </div>
                    </td>
                    <td>Amit Patel</td>
                    <td><EMAIL></td>
                    <td>+91-9876543212</td>
                    <td>Ahmedabad, Gujarat</td>
                    <td><span class="badge badge-secondary">Both</span></td>
                    <td>42</td>
                    <td><span class="badge badge-warning">Pending</span></td>
                    <td>
                        <div class="flex space-x-2">
                            <button class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <div class="flex items-center justify-between mt-6">
        <div class="text-sm text-gray-500">
            Showing 1 to 10 of 456 results
        </div>
        <div class="flex space-x-2">
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Previous</button>
            <button class="px-3 py-1 bg-primary text-white rounded-md text-sm">1</button>
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">2</button>
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">3</button>
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Next</button>
        </div>
    </div>
</div>

<?php
// Page-specific JavaScript
$pageScripts = "
<script>
function pageInit() {
    console.log('Vendor management page loaded');
    
    // Add event listeners for action buttons
    document.querySelectorAll('.fa-eye').forEach(btn => {
        btn.addEventListener('click', function() {
            showAlert('info', 'View Vendor', 'Vendor details would be shown here.');
        });
    });
    
    document.querySelectorAll('.fa-edit').forEach(btn => {
        btn.addEventListener('click', function() {
            showAlert('info', 'Edit Vendor', 'Vendor edit form would be shown here.');
        });
    });
    
    document.querySelectorAll('.fa-trash').forEach(btn => {
        btn.addEventListener('click', function() {
            showConfirm('Delete Vendor', 'Are you sure you want to delete this vendor?', function() {
                showAlert('success', 'Deleted', 'Vendor has been deleted successfully.');
            });
        });
    });
}
</script>
";

// Include footer
include_once '../layouts/footer.php';
?>
