/**
 * Alyanka CRM - Main JavaScript
 *
 * This file contains all the JavaScript functionality for the CRM.
 */

document.addEventListener("DOMContentLoaded", function () {
  // Sidebar toggle functionality
  const sidebarToggle = document.getElementById("sidebar-toggle");
  const sidebar = document.getElementById("sidebar");
  const mainContent = document.getElementById("main-content");
  const sidebarToggleContainer = document.getElementById(
    "sidebar-toggle-container"
  );
  const sidebarLogo = document.getElementById("sidebar-logo");
  const sidebarLogoSmall = document.getElementById("sidebar-logo-small");
  const topNav = document.getElementById("top-nav");
  const sidebarOverlay = document.getElementById("sidebar-overlay");
  const mobileMenuButton = document.getElementById("mobile-menu-button");

  // Submenu toggle functionality is now handled in sidebar.js
  // This code is kept for reference but is not active

  // Sidebar collapse/expand functionality is now handled in sidebar.js
  // This code is kept for backward compatibility but is not active

  // Mobile menu functionality
  if (mobileMenuButton) {
    mobileMenuButton.addEventListener("click", function () {
      sidebar.classList.toggle("sidebar-open");
      sidebarOverlay.classList.toggle("show");
    });
  }

  // Close sidebar when clicking on overlay
  if (sidebarOverlay) {
    sidebarOverlay.addEventListener("click", function () {
      sidebar.classList.remove("sidebar-open");
      sidebarOverlay.classList.remove("show");
    });
  }

  // User dropdown
  const userMenuButton = document.getElementById("user-menu-button");
  const userDropdown = document.getElementById("user-dropdown");

  // Notification dropdown
  const notificationButton = document.getElementById("notification-button");
  const notificationDropdown = document.getElementById("notification-dropdown");

  // Debug to check if elements exist
  console.log('User menu button:', userMenuButton);
  console.log('User dropdown:', userDropdown);
  console.log('Notification button:', notificationButton);
  console.log('Notification dropdown:', notificationDropdown);

  // Make sure dropdowns are initially hidden but visible when toggled
  if (userDropdown) {
    userDropdown.classList.add("hidden");
    userDropdown.style.display = "";
  }
  
  if (notificationDropdown) {
    notificationDropdown.classList.add("hidden");
    notificationDropdown.style.display = "";
  }

  // Enhanced notification dropdown toggle
  if (notificationButton && notificationDropdown) {
    notificationButton.addEventListener("click", function (e) {
      e.preventDefault();
      e.stopPropagation();
      
      // Toggle visibility
      const isHidden = notificationDropdown.classList.contains("hidden");
      
      if (isHidden) {
        notificationDropdown.classList.remove("hidden");
        if (userDropdown) userDropdown.classList.add("hidden");
      } else {
        notificationDropdown.classList.add("hidden");
      }
    });
  }

  // Enhanced user dropdown toggle
  if (userMenuButton && userDropdown) {
    userMenuButton.addEventListener("click", function (e) {
      e.preventDefault();
      e.stopPropagation();
      
      // Toggle visibility
      const isHidden = userDropdown.classList.contains("hidden");
      
      if (isHidden) {
        userDropdown.classList.remove("hidden");
        if (notificationDropdown) notificationDropdown.classList.add("hidden");
      } else {
        userDropdown.classList.add("hidden");
      }
    });
  }

  // Close dropdowns when clicking outside
  document.addEventListener("click", function (e) {
    if (
      notificationButton &&
      notificationDropdown &&
      !notificationButton.contains(e.target) &&
      !notificationDropdown.contains(e.target)
    ) {
      notificationDropdown.classList.add("hidden");
    }

    if (
      userMenuButton &&
      userDropdown &&
      !userMenuButton.contains(e.target) &&
      !userDropdown.contains(e.target)
    ) {
      userDropdown.classList.add("hidden");
    }
  });
});
