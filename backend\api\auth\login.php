<?php

/**
 * Login API Endpoint
 * Handles user authentication
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../../config/database.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!isset($input['email']) || !isset($input['password'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Email and password required']);
        exit;
    }

    $email = filter_var($input['email'], FILTER_SANITIZE_EMAIL);
    $password = $input['password'];

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid email format']);
        exit;
    }

    // Demo credentials for testing (remove in production)
    $demoCredentials = [
        [
            'id' => 1,
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => 'admin123',
            'role' => 'admin'
        ],
        [
            'id' => 2,
            'name' => 'Manager User',
            'email' => '<EMAIL>',
            'password' => 'manager123',
            'role' => 'manager'
        ],
        [
            'id' => 3,
            'name' => 'Employee User',
            'email' => '<EMAIL>',
            'password' => 'employee123',
            'role' => 'employee'
        ]
    ];

    // Check demo credentials first
    $demoUser = null;
    foreach ($demoCredentials as $cred) {
        if ($cred['email'] === $email && $cred['password'] === $password) {
            $demoUser = $cred;
            break;
        }
    }

    if ($demoUser) {
        // Start session
        session_start();
        $_SESSION['user_id'] = $demoUser['id'];
        $_SESSION['user_name'] = $demoUser['name'];
        $_SESSION['user_email'] = $demoUser['email'];
        $_SESSION['user_role'] = $demoUser['role'];

        echo json_encode([
            'success' => true,
            'message' => 'Login successful',
            'user' => [
                'id' => $demoUser['id'],
                'name' => $demoUser['name'],
                'email' => $demoUser['email'],
                'role' => $demoUser['role']
            ]
        ]);
        exit;
    }

    // If not demo user, check database (if available)
    try {
        $stmt = $database->execute(
            "SELECT id, name, email, password, role, status FROM users WHERE email = ? AND status = 1",
            [$email]
        );

        $user = $stmt->fetch();

        if ($user && password_verify($password, $user['password'])) {
            // Start session
            session_start();
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_role'] = $user['role'];

            echo json_encode([
                'success' => true,
                'message' => 'Login successful',
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ]
            ]);
        } else {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Invalid credentials']);
        }
    } catch (Exception $e) {
        // Database not available, already handled demo credentials above
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Invalid credentials']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
