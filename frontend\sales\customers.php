<?php
/**
 * Enterprise ERP System - Customer Management
 * 
 * This page handles customer listing, creation, and management
 */

// Page configuration
$pageTitle = "Customer Management";
$pageHeader = "Customers";
$pageDescription = "Manage your customer database and relationships.";

$breadcrumbs = [
    ['title' => 'Dashboard', 'url' => '../dashboard/index.php'],
    ['title' => 'Sales'],
    ['title' => 'Customers']
];

// Include main layout
include_once '../layouts/main.php';
?>

<!-- Page Actions -->
<div class="flex justify-between items-center mb-6">
    <div class="flex space-x-3">
        <button class="btn-primary">
            <i class="fas fa-plus mr-2"></i>Add Customer
        </button>
        <button class="btn-secondary">
            <i class="fas fa-upload mr-2"></i>Import
        </button>
        <button class="btn-secondary">
            <i class="fas fa-download mr-2"></i>Export
        </button>
    </div>
    
    <div class="flex space-x-3">
        <div class="relative">
            <input type="text" placeholder="Search customers..." 
                   class="form-input w-64 pl-10">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
        </div>
        <select class="form-select">
            <option>All Status</option>
            <option>Active</option>
            <option>Inactive</option>
        </select>
    </div>
</div>

<!-- Customer Stats -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="card">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                <i class="fas fa-users text-blue-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500">Total Customers</p>
                <p class="text-xl font-bold text-gray-900">1,247</p>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                <i class="fas fa-user-check text-green-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500">Active Customers</p>
                <p class="text-xl font-bold text-gray-900">1,156</p>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                <i class="fas fa-star text-yellow-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500">VIP Customers</p>
                <p class="text-xl font-bold text-gray-900">89</p>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                <i class="fas fa-chart-line text-purple-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500">This Month</p>
                <p class="text-xl font-bold text-gray-900">23</p>
            </div>
        </div>
    </div>
</div>

<!-- Customer Table -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Customer List</h3>
    </div>
    
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>Customer ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Location</th>
                    <th>Total Orders</th>
                    <th>Total Value</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="font-medium">#CUST-001</td>
                    <td>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                <span class="text-xs font-medium">AC</span>
                            </div>
                            <div>
                                <div class="font-medium">Acme Corporation</div>
                                <div class="text-sm text-gray-500">John Doe</div>
                            </div>
                        </div>
                    </td>
                    <td><EMAIL></td>
                    <td>+91-9876543210</td>
                    <td>Mumbai, Maharashtra</td>
                    <td>15</td>
                    <td class="text-currency">₹2,45,000</td>
                    <td><span class="badge badge-success">Active</span></td>
                    <td>
                        <div class="flex space-x-2">
                            <button class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td class="font-medium">#CUST-002</td>
                    <td>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                <span class="text-xs font-medium">TI</span>
                            </div>
                            <div>
                                <div class="font-medium">Tech Innovations Ltd</div>
                                <div class="text-sm text-gray-500">Sarah Smith</div>
                            </div>
                        </div>
                    </td>
                    <td><EMAIL></td>
                    <td>+91-9876543211</td>
                    <td>Bangalore, Karnataka</td>
                    <td>8</td>
                    <td class="text-currency">₹1,85,000</td>
                    <td><span class="badge badge-success">Active</span></td>
                    <td>
                        <div class="flex space-x-2">
                            <button class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                
                <tr>
                    <td class="font-medium">#CUST-003</td>
                    <td>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                <span class="text-xs font-medium">GS</span>
                            </div>
                            <div>
                                <div class="font-medium">Global Solutions</div>
                                <div class="text-sm text-gray-500">Mike Johnson</div>
                            </div>
                        </div>
                    </td>
                    <td><EMAIL></td>
                    <td>+91-9876543212</td>
                    <td>Delhi, Delhi</td>
                    <td>22</td>
                    <td class="text-currency">₹3,75,000</td>
                    <td><span class="badge badge-warning">Pending</span></td>
                    <td>
                        <div class="flex space-x-2">
                            <button class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <div class="flex items-center justify-between mt-6">
        <div class="text-sm text-gray-500">
            Showing 1 to 10 of 1,247 results
        </div>
        <div class="flex space-x-2">
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Previous</button>
            <button class="px-3 py-1 bg-primary text-white rounded-md text-sm">1</button>
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">2</button>
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">3</button>
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Next</button>
        </div>
    </div>
</div>

<?php
// Page-specific JavaScript
$pageScripts = "
<script>
function pageInit() {
    console.log('Customer management page loaded');
    
    // Add event listeners for action buttons
    document.querySelectorAll('.fa-eye').forEach(btn => {
        btn.addEventListener('click', function() {
            showAlert('info', 'View Customer', 'Customer details would be shown here.');
        });
    });
    
    document.querySelectorAll('.fa-edit').forEach(btn => {
        btn.addEventListener('click', function() {
            showAlert('info', 'Edit Customer', 'Customer edit form would be shown here.');
        });
    });
    
    document.querySelectorAll('.fa-trash').forEach(btn => {
        btn.addEventListener('click', function() {
            showConfirm('Delete Customer', 'Are you sure you want to delete this customer?', function() {
                showAlert('success', 'Deleted', 'Customer has been deleted successfully.');
            });
        });
    });
}
</script>
";

// Include footer
include_once '../layouts/footer.php';
?>
