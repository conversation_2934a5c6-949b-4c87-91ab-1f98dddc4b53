<?php
/**
 * Enterprise ERP System - Main Dashboard
 * 
 * This is the main dashboard page showing key metrics, charts,
 * and quick access to important functions.
 */

// Page configuration
$pageTitle = "Dashboard";
$pageHeader = "Dashboard";
$pageDescription = "Welcome to your Enterprise ERP System. Here's an overview of your business performance.";

$breadcrumbs = [
    ['title' => 'Dashboard']
];

// Include main layout
include_once '../layouts/main.php';
?>

<!-- Dashboard Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Sales Card -->
    <div class="bg-white rounded-lg shadow-custom p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                    <i class="fas fa-chart-line text-green-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Sales</p>
                <p class="text-2xl font-bold text-gray-900">₹12,45,000</p>
                <p class="text-sm text-green-600">
                    <i class="fas fa-arrow-up"></i> 12% from last month
                </p>
            </div>
        </div>
    </div>

    <!-- Total Purchase Card -->
    <div class="bg-white rounded-lg shadow-custom p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                    <i class="fas fa-shopping-cart text-blue-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Purchase</p>
                <p class="text-2xl font-bold text-gray-900">₹8,75,000</p>
                <p class="text-sm text-blue-600">
                    <i class="fas fa-arrow-up"></i> 8% from last month
                </p>
            </div>
        </div>
    </div>

    <!-- Inventory Value Card -->
    <div class="bg-white rounded-lg shadow-custom p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                    <i class="fas fa-boxes text-purple-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Inventory Value</p>
                <p class="text-2xl font-bold text-gray-900">₹15,20,000</p>
                <p class="text-sm text-purple-600">
                    <i class="fas fa-arrow-down"></i> 3% from last month
                </p>
            </div>
        </div>
    </div>

    <!-- Active Employees Card -->
    <div class="bg-white rounded-lg shadow-custom p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-100 rounded-md flex items-center justify-center">
                    <i class="fas fa-users text-orange-600"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Active Employees</p>
                <p class="text-2xl font-bold text-gray-900">156</p>
                <p class="text-sm text-orange-600">
                    <i class="fas fa-arrow-up"></i> 5 new this month
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Sales Chart -->
    <div class="bg-white rounded-lg shadow-custom p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Sales Overview</h3>
            <select class="text-sm border border-gray-300 rounded-md px-3 py-1">
                <option>Last 7 days</option>
                <option>Last 30 days</option>
                <option>Last 3 months</option>
            </select>
        </div>
        <div class="h-64">
            <canvas id="salesChart"></canvas>
        </div>
    </div>

    <!-- Top Products -->
    <div class="bg-white rounded-lg shadow-custom p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Selling Products</h3>
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-200 rounded-md flex items-center justify-center">
                        <i class="fas fa-cube text-gray-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Product A</p>
                        <p class="text-xs text-gray-500">SKU: PA001</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-sm font-medium text-gray-900">₹45,000</p>
                    <p class="text-xs text-gray-500">120 units</p>
                </div>
            </div>
            
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-200 rounded-md flex items-center justify-center">
                        <i class="fas fa-cube text-gray-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Product B</p>
                        <p class="text-xs text-gray-500">SKU: PB002</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-sm font-medium text-gray-900">₹38,500</p>
                    <p class="text-xs text-gray-500">95 units</p>
                </div>
            </div>
            
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-200 rounded-md flex items-center justify-center">
                        <i class="fas fa-cube text-gray-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Product C</p>
                        <p class="text-xs text-gray-500">SKU: PC003</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-sm font-medium text-gray-900">₹32,000</p>
                    <p class="text-xs text-gray-500">80 units</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities and Quick Actions -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Recent Activities -->
    <div class="lg:col-span-2 bg-white rounded-lg shadow-custom p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
        <div class="space-y-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">New sales order #SO-2024-001 created</p>
                    <p class="text-xs text-gray-500">2 minutes ago</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-blue-600 text-sm"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Purchase order #PO-2024-015 approved</p>
                    <p class="text-xs text-gray-500">15 minutes ago</p>
                </div>
            </div>
            
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-sm"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Low stock alert for Product XYZ</p>
                    <p class="text-xs text-gray-500">1 hour ago</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-custom p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div class="space-y-3">
            <a href="../sales/orders.php" 
               class="block w-full bg-primary text-white text-center py-2 px-4 rounded-md hover:bg-primary-dark transition-colors">
                <i class="fas fa-plus mr-2"></i>New Sales Order
            </a>
            
            <a href="../purchase/orders.php" 
               class="block w-full bg-secondary text-white text-center py-2 px-4 rounded-md hover:bg-gray-600 transition-colors">
                <i class="fas fa-plus mr-2"></i>New Purchase Order
            </a>
            
            <a href="../inventory/products.php" 
               class="block w-full bg-success text-white text-center py-2 px-4 rounded-md hover:bg-green-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>Add Product
            </a>
            
            <a href="../hr/employees.php" 
               class="block w-full bg-info text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>Add Employee
            </a>
        </div>
    </div>
</div>

<?php
// Page-specific JavaScript
$pageScripts = "
<script>
// Initialize dashboard charts
function pageInit() {
    initializeSalesChart();
}

function initializeSalesChart() {
    const ctx = document.getElementById('salesChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Sales',
                data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
                borderColor: '#1e40af',
                backgroundColor: 'rgba(30, 64, 175, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₹' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}
</script>
";

// Include footer
include_once '../layouts/footer.php';
?>
