        <!-- End of page content -->
        </main>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Mobile menu toggle
                const mobileMenuButton = document.getElementById('mobile-menu-button');
                const sidebar = document.getElementById('sidebar');
                const sidebarOverlay = document.getElementById('sidebar-overlay');

                if (mobileMenuButton && sidebar && sidebarOverlay) {
                    console.log('Mobile menu button found, adding event listener');
                    mobileMenuButton.addEventListener('click', function() {
                        console.log('Mobile menu button clicked');
                        sidebar.classList.toggle('sidebar-open');
                        sidebarOverlay.classList.toggle('show');
                    });

                    sidebarOverlay.addEventListener('click', function() {
                        sidebar.classList.remove('sidebar-open');
                        sidebarOverlay.classList.remove('show');
                    });
                } else {
                    console.log('Mobile menu elements not found:', {
                        mobileMenuButton: !!mobileMenuButton,
                        sidebar: !!sidebar,
                        sidebarOverlay: !!sidebarOverlay
                    });
                }

                // Notification dropdown toggle
                const notificationButton = document.getElementById('notification-button');
                const notificationDropdown = document.getElementById('notification-dropdown');

                if (notificationButton && notificationDropdown) {
                    notificationButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        notificationDropdown.classList.toggle('hidden');

                        // Hide user dropdown if open
                        if (userDropdown && !userDropdown.classList.contains('hidden')) {
                            userDropdown.classList.add('hidden');
                        }
                    });
                }

                // User dropdown toggle
                const userMenuButton = document.getElementById('user-menu-button');
                const userDropdown = document.getElementById('user-dropdown');

                if (userMenuButton && userDropdown) {
                    userMenuButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        userDropdown.classList.toggle('hidden');

                        // Hide notification dropdown if open
                        if (notificationDropdown && !notificationDropdown.classList.contains('hidden')) {
                            notificationDropdown.classList.add('hidden');
                        }
                    });
                }

                // Close dropdowns when clicking outside
                document.addEventListener('click', function(e) {
                    if (notificationDropdown && !notificationDropdown.contains(e.target) && !notificationButton.contains(e.target)) {
                        notificationDropdown.classList.add('hidden');
                    }

                    if (userDropdown && !userDropdown.contains(e.target) && !userMenuButton.contains(e.target)) {
                        userDropdown.classList.add('hidden');
                    }
                });

                <?php if (isset($pageScript)): ?>
                    // Page specific script
                    <?php echo $pageScript; ?>
                <?php endif; ?>
            });
        </script>

        <?php if (isset($extraScripts)): ?>
            <!-- Extra scripts -->
            <?php echo $extraScripts; ?>
        <?php endif; ?>
        </body>

        </html>