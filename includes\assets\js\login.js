document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;
            
            // Validate form data
            if (!validateEmail(email)) {
                showAlert('error', 'Invalid Email', 'Please enter a valid email address.');
                return;
            }
            
            if (password.length < 6) {
                showAlert('error', 'Invalid Password', 'Password must be at least 6 characters long.');
                return;
            }
            
            // Show loading state
            const submitButton = loginForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = '<div class="flex items-center justify-center"><div class="apple-loader mr-2 w-5 h-5"></div> Signing in...</div>';
            
            // Send AJAX request to login API
            const formData = {
                email: email,
                password: password,
                remember: remember
            };
            
            // AJAX request
            fetch('../backend/api/login.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
                
                if (data.success) {
                    // Show success message
                    showAlert('success', 'Login Successful', 'Redirecting to dashboard...');
                    
                    // Redirect to dashboard after a short delay
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);
                } else {
                    // Show error message
                    showAlert('error', 'Login Failed', data.message || 'Invalid email or password.');
                }
            })
            .catch(error => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
                
                // Show error message
                showAlert('error', 'Connection Error', 'Could not connect to the server. Please try again later.');
                console.error('Error:', error);
            });
        });
    }
    
    // Email validation function
    function validateEmail(email) {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }
    
    // SweetAlert function
    function showAlert(icon, title, text) {
        Swal.fire({
            icon: icon,
            title: title,
            text: text,
            confirmButtonColor: '#0071e3',
            confirmButtonText: 'OK',
            customClass: {
                container: 'font-sans',
                popup: 'rounded-2xl',
                confirmButton: 'rounded-lg'
            }
        });
    }
});
