<?php

/**
 * Enterprise ERP System - Footer Layout
 * 
 * This file contains the main footer and JavaScript initialization
 * for the ERP system.
 */
?>

</main>

<!-- Footer -->
<footer class="bg-white border-t border-gray-200 lg:ml-64">
    <div class="px-4 py-6 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
            <div class="text-sm text-gray-500">
                © <?php echo date('Y'); ?> Enterprise ERP System. All rights reserved.
            </div>
            <div class="flex space-x-6 mt-4 md:mt-0">
                <a href="#" class="text-sm text-gray-500 hover:text-gray-900">Privacy Policy</a>
                <a href="#" class="text-sm text-gray-500 hover:text-gray-900">Terms of Service</a>
                <a href="#" class="text-sm text-gray-500 hover:text-gray-900">Support</a>
            </div>
        </div>
    </div>
</footer>

<!-- JavaScript -->
<script>
    // Global JavaScript functions and initialization
    document.addEventListener('DOMContentLoaded', function() {

        // Sidebar elements
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        const mainContent = document.querySelector('main');

        // Mobile menu toggle
        if (mobileMenuButton && sidebar && sidebarOverlay) {
            mobileMenuButton.addEventListener('click', function() {
                // For mobile, just show/hide sidebar with overlay
                sidebar.classList.toggle('-translate-x-full');
                sidebarOverlay.classList.toggle('hidden');
            });
        }

        // Desktop sidebar toggle
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                // Toggle between expanded and collapsed states
                const isExpanded = sidebar.classList.contains('sidebar-expanded');

                if (isExpanded) {
                    // Collapse to icons only
                    sidebar.classList.remove('sidebar-expanded');
                    sidebar.classList.add('sidebar-collapsed');

                    // Adjust main content margin for collapsed sidebar (80px)
                    if (mainContent) {
                        mainContent.classList.remove('lg:ml-64');
                        mainContent.classList.add('lg:ml-20'); // 5rem = 80px
                    }
                } else {
                    // Expand to full width
                    sidebar.classList.remove('sidebar-collapsed');
                    sidebar.classList.add('sidebar-expanded');

                    // Adjust main content margin for expanded sidebar (256px)
                    if (mainContent) {
                        mainContent.classList.remove('lg:ml-20');
                        mainContent.classList.add('lg:ml-64'); // 16rem = 256px
                    }
                }
            });
        }

        // Close mobile sidebar when clicking overlay
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', function() {
                sidebar.classList.add('-translate-x-full');
                sidebarOverlay.classList.add('hidden');
            });
        }

        // Handle window resize to ensure proper sidebar state
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 1024) { // lg breakpoint
                // On large screens, hide overlay and ensure sidebar is visible
                if (sidebarOverlay) {
                    sidebarOverlay.classList.add('hidden');
                }
                // Don't automatically show sidebar on desktop - let user control it
            } else {
                // On small screens, ensure sidebar is hidden by default
                if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                    sidebar.classList.add('-translate-x-full');
                }
                if (sidebarOverlay) {
                    sidebarOverlay.classList.add('hidden');
                }
            }
        });

        // Notifications dropdown
        const notificationsButton = document.getElementById('notifications-button');
        const notificationsDropdown = document.getElementById('notifications-dropdown');

        if (notificationsButton && notificationsDropdown) {
            notificationsButton.addEventListener('click', function(e) {
                e.stopPropagation();
                notificationsDropdown.classList.toggle('hidden');

                // Close user dropdown if open
                const userDropdown = document.getElementById('user-dropdown');
                if (userDropdown && !userDropdown.classList.contains('hidden')) {
                    userDropdown.classList.add('hidden');
                }
            });
        }

        // User menu dropdown
        const userMenuButton = document.getElementById('user-menu-button');
        const userDropdown = document.getElementById('user-dropdown');

        if (userMenuButton && userDropdown) {
            userMenuButton.addEventListener('click', function(e) {
                e.stopPropagation();
                userDropdown.classList.toggle('hidden');

                // Close notifications dropdown if open
                if (notificationsDropdown && !notificationsDropdown.classList.contains('hidden')) {
                    notificationsDropdown.classList.add('hidden');
                }
            });
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (notificationsDropdown && !notificationsDropdown.contains(e.target) &&
                !notificationsButton.contains(e.target)) {
                notificationsDropdown.classList.add('hidden');
            }

            if (userDropdown && !userDropdown.contains(e.target) &&
                !userMenuButton.contains(e.target)) {
                userDropdown.classList.add('hidden');
            }
        });

        // Initialize tooltips (if using any tooltip library)
        initializeTooltips();

        // Page-specific initialization
        if (typeof pageInit === 'function') {
            pageInit();
        }
    });

    // Horizontal submenu toggle function
    function toggleHorizontalSubmenu(submenuId) {
        // Close all other dropdowns first
        document.querySelectorAll('[id$="-dropdown"]').forEach(dropdown => {
            if (dropdown.id !== submenuId) {
                dropdown.classList.add('hidden');
            }
        });

        const submenu = document.getElementById(submenuId);
        if (submenu) {
            submenu.classList.toggle('hidden');
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('[onclick*="toggleHorizontalSubmenu"]')) {
            document.querySelectorAll('[id$="-dropdown"]').forEach(dropdown => {
                dropdown.classList.add('hidden');
            });
        }
    });

    // Legacy submenu toggle function (for vertical sidebar if needed)
    function toggleSubmenu(submenuId) {
        const submenu = document.getElementById(submenuId);
        if (submenu) {
            const isHidden = submenu.classList.contains('hidden');

            if (isHidden) {
                submenu.classList.remove('hidden');
            } else {
                submenu.classList.add('hidden');
            }

            // Rotate chevron icon
            const button = submenu.previousElementSibling;
            const chevron = button.querySelector('.fa-chevron-down');
            if (chevron) {
                chevron.style.transform = isHidden ? 'rotate(180deg)' : 'rotate(0deg)';
                chevron.style.transition = 'transform 0.3s ease';
            }
        }
    }

    // Initialize tooltips function
    function initializeTooltips() {
        // Add tooltip initialization here if using a tooltip library
    }

    // Global utility functions
    function showAlert(type, title, message) {
        const icons = {
            success: 'success',
            error: 'error',
            warning: 'warning',
            info: 'info'
        };

        Swal.fire({
            icon: icons[type] || 'info',
            title: title,
            text: message,
            confirmButtonColor: '#1e40af',
            confirmButtonText: 'OK'
        });
    }

    function showConfirm(title, message, callback) {
        Swal.fire({
            title: title,
            text: message,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, proceed!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed && typeof callback === 'function') {
                callback();
            }
        });
    }

    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR'
        }).format(amount);
    }

    function formatDate(date) {
        return new Intl.DateTimeFormat('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }).format(new Date(date));
    }

    // AJAX helper function
    function makeRequest(url, method = 'GET', data = null) {
        return fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: data ? JSON.stringify(data) : null
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            });
    }
</script>

<!-- Custom page scripts -->
<?php if (isset($pageScripts)) echo $pageScripts; ?>

</body>

</html>