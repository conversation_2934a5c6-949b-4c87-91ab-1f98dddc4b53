<?php

/**
 * Enterprise ERP System - Main Header Layout
 * 
 * This file contains the main header with navigation, user menu,
 * and responsive design for the ERP system.
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Get current directory to determine correct path to login
    $currentDir = basename(dirname($_SERVER['PHP_SELF']));
    if ($currentDir === 'auth') {
        // Already in auth directory, don't redirect
    } else {
        header("Location: ../auth/login.php");
        exit;
    }
}

// Get user information
$userName = $_SESSION['user_name'] ?? 'User';
$userEmail = $_SESSION['user_email'] ?? '';
$userRole = $_SESSION['user_role'] ?? 'employee';

// Generate user avatar
$userAvatar = 'https://ui-avatars.com/api/?name=' . urlencode($userName) . '&background=0071e3&color=fff&size=40';

// Set default page title if not provided
if (!isset($pageTitle)) {
    $pageTitle = "Enterprise ERP System";
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Enterprise ERP</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1e40af',
                        'primary-dark': '#1e3a8a',
                        'secondary': '#64748b',
                        'success': '#059669',
                        'warning': '#d97706',
                        'danger': '#dc2626',
                        'info': '#0284c7',
                        'light': '#f8fafc',
                        'dark': '#0f172a'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    boxShadow: {
                        'custom': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <?php if (isset($extraStyles)) echo $extraStyles; ?>
</head>

<body class="bg-gray-50 font-sans">

    <!-- Mobile Sidebar Overlay -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

    <!-- Top Navigation Bar -->
    <nav class="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-30">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Left side - Logo -->
                <div class="flex items-center">
                    <!-- Mobile menu button -->
                    <button type="button" id="mobile-menu-button"
                        class="lg:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary mr-3">
                        <i class="fas fa-bars text-lg"></i>
                    </button>

                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                            <span class="text-white font-bold text-sm">E</span>
                        </div>
                        <h1 class="text-xl font-bold text-gray-900">Enterprise ERP</h1>
                    </div>
                </div>

                <!-- Right side navbar -->
                <div class="flex items-center space-x-4">
                    <!-- Search -->
                    <div class="hidden md:block">
                        <div class="relative">
                            <input type="text" placeholder="Search..."
                                class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications -->
                    <div class="relative">
                        <button type="button" id="notifications-button"
                            class="p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-lg">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400"></span>
                        </button>

                        <!-- Notifications dropdown -->
                        <div id="notifications-dropdown" class="hidden origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                            <div class="py-1">
                                <div class="px-4 py-2 text-sm font-medium text-gray-900 border-b">Notifications</div>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-info-circle text-blue-500"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium">New order received</p>
                                            <p class="text-xs text-gray-500">2 minutes ago</p>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="relative">
                        <button type="button" id="user-menu-button"
                            class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-primary">
                            <img class="h-8 w-8 rounded-full" src="<?php echo $userAvatar; ?>" alt="User avatar">
                            <span class="hidden md:block ml-2 text-gray-700"><?php echo $userName; ?></span>
                            <i class="hidden md:block fas fa-chevron-down ml-1 text-gray-400"></i>
                        </button>

                        <!-- User dropdown -->
                        <div id="user-dropdown" class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                            <div class="px-4 py-3 border-b">
                                <p class="text-sm font-medium text-gray-900"><?php echo $userName; ?></p>
                                <p class="text-sm text-gray-500"><?php echo $userEmail; ?></p>
                            </div>
                            <div class="py-1">
                                <a href="../profile/index.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-user mr-2"></i>Profile
                                </a>
                                <a href="../settings/index.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-cog mr-2"></i>Settings
                                </a>
                                <div class="border-t border-gray-100"></div>
                                <a href="../auth/logout.php" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-50">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Sign out
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>