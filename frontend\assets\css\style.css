/**
 * Enterprise ERP System - Custom Styles
 * 
 * Additional CSS styles for the ERP system that complement Tailwind CSS
 */

/* Sidebar Styles */
#sidebar {
  transition: width 0.3s ease-in-out, transform 0.3s ease-in-out;
}

/* Sidebar collapsed state - show only icons */
#sidebar.sidebar-collapsed {
  width: 5rem !important; /* 80px - better for icons and small logo */
  overflow: hidden !important;
  background-color: #f9fafb;
  border-right: 1px solid #e5e7eb;
}

/* Hide all text elements completely */
#sidebar.sidebar-collapsed .sidebar-text,
#sidebar.sidebar-collapsed span:not(.sr-only),
#sidebar.sidebar-collapsed .ml-3,
#sidebar.sidebar-collapsed .ml-2,
#sidebar.sidebar-collapsed .flex-1 {
  display: none !important;
  visibility: hidden !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  opacity: 0 !important;
}

#sidebar.sidebar-collapsed .submenu {
  display: none !important;
}

#sidebar.sidebar-collapsed h3 {
  display: none !important;
}

#sidebar.sidebar-collapsed .mt-6 {
  margin-top: 0.75rem !important;
}

/* Ensure all menu sections are visible */
#sidebar.sidebar-collapsed .sidebar-container {
  gap: 0.5rem !important;
}

/* Perfect centering for all buttons and links */
#sidebar.sidebar-collapsed button,
#sidebar.sidebar-collapsed a {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 3.5rem !important;
  height: 3rem !important;
  padding: 0 !important;
  margin: 0.25rem auto !important;
  border-radius: 0.5rem !important;
  position: relative !important;
}

#sidebar.sidebar-collapsed .fa-chevron-down {
  display: none !important;
}

#sidebar.sidebar-collapsed .sidebar-container {
  padding: 1rem 0.5rem !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Logo handling in collapsed state */
#sidebar.sidebar-collapsed .sidebar-logo-expanded {
  display: none !important;
}

#sidebar.sidebar-collapsed .sidebar-logo-collapsed {
  display: flex !important;
}

#sidebar.sidebar-expanded .sidebar-logo-expanded {
  display: block !important;
}

#sidebar.sidebar-expanded .sidebar-logo-collapsed {
  display: none !important;
}

/* Perfect icon centering */
#sidebar.sidebar-collapsed i {
  margin: 0 !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.125rem !important;
  flex-shrink: 0 !important;
  color: #4b5563 !important;
  transition: color 0.2s ease !important;
}

#sidebar.sidebar-collapsed button:hover i,
#sidebar.sidebar-collapsed a:hover i {
  color: #1f2937 !important;
}

#sidebar.sidebar-collapsed .bg-primary i {
  color: white !important;
}

/* Remove any flex properties that might interfere */
#sidebar.sidebar-collapsed .flex,
#sidebar.sidebar-collapsed .flex-1,
#sidebar.sidebar-collapsed .items-center {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Hide list styling */
#sidebar.sidebar-collapsed ul {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 0.25rem !important;
  margin: 0 !important;
  padding: 0 !important;
}

#sidebar.sidebar-collapsed li {
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
}

/* Additional cleanup for any remaining text elements */
#sidebar.sidebar-collapsed .text-left,
#sidebar.sidebar-collapsed .whitespace-nowrap,
#sidebar.sidebar-collapsed .tracking-wider,
#sidebar.sidebar-collapsed .uppercase,
#sidebar.sidebar-collapsed .font-semibold,
#sidebar.sidebar-collapsed .font-medium {
  display: none !important;
}

/* Ensure perfect button sizing */
#sidebar.sidebar-collapsed button:hover,
#sidebar.sidebar-collapsed a:hover {
  background-color: #f3f4f6 !important;
  transform: none !important;
}

/* Remove any potential text shadows or effects */
#sidebar.sidebar-collapsed * {
  text-shadow: none !important;
}

/* Force hide any remaining spans */
#sidebar.sidebar-collapsed span:not(.sr-only) {
  display: none !important;
  visibility: hidden !important;
  position: absolute !important;
  left: -9999px !important;
}

/* Expanded state */
#sidebar.sidebar-expanded .sidebar-text {
  opacity: 1;
  visibility: visible;
  transition: all 0.3s ease-in-out;
}

/* Tooltip for collapsed sidebar */
#sidebar.sidebar-collapsed [title]:hover::after {
  content: attr(title);
  position: absolute;
  left: 4.5rem;
  background: #1f2937;
  color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for sidebar */
#sidebar::-webkit-scrollbar {
  width: 6px;
}

#sidebar::-webkit-scrollbar-track {
  background: #f1f5f9;
}

#sidebar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

#sidebar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Sidebar overlay for mobile */
#sidebar-overlay {
  transition: opacity 0.3s ease-in-out;
}

#sidebar-overlay.show {
  display: block !important;
}

/* Ensure main content adjusts properly */
main {
  transition: margin-left 0.3s ease-in-out;
}

/* Desktop sidebar states */
@media (min-width: 1024px) {
  /* Additional desktop-specific collapsed styles */
  #sidebar.sidebar-collapsed {
    border-right: 1px solid #e5e7eb;
  }

  /* Main content adjustments */
  main.lg\:ml-20 {
    margin-left: 5rem !important;
  }

  /* Sidebar toggle button hover effect */
  #sidebar-toggle:hover {
    background-color: #f3f4f6;
  }

  /* Submenu animations */
  .submenu {
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .submenu.hidden {
    max-height: 0;
    opacity: 0;
  }

  .submenu:not(.hidden) {
    max-height: 500px;
    opacity: 1;
  }

  /* Tooltip styles for collapsed sidebar */
  #sidebar.sidebar-collapsed [title] {
    position: relative;
  }

  #sidebar.sidebar-collapsed [title]:hover::after {
    content: attr(title);
    position: fixed;
    left: 6rem;
    top: 50%;
    transform: translateY(-50%);
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    pointer-events: none;
    animation: fadeIn 0.2s ease-in-out;
  }

  #sidebar.sidebar-collapsed [title]:hover::before {
    content: "";
    position: fixed;
    left: 5.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-right: 5px solid #1f2937;
    z-index: 1000;
    pointer-events: none;
  }
}

/* Active menu item styling */
.bg-primary {
  background-color: #3b82f6 !important;
  color: white !important;
}

.bg-primary:hover {
  background-color: #2563eb !important;
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s ease;
}

/* Custom button hover effects */
.btn-primary {
  @apply bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors;
}

.btn-secondary {
  @apply bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors;
}

.btn-success {
  @apply bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors;
}

.btn-danger {
  @apply bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors;
}

.btn-warning {
  @apply bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 transition-colors;
}

.btn-info {
  @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors;
}

/* Custom form styles */
.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
}

.form-select {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white;
}

.form-textarea {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-vertical;
}

/* Custom table styles */
.table-responsive {
  @apply overflow-x-auto shadow-sm;
}

.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table thead {
  @apply bg-gray-50;
}

.table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table tbody tr:nth-child(even) {
  @apply bg-gray-50;
}

.table tbody tr:hover {
  @apply bg-gray-100;
}

/* Custom card styles */
.card {
  @apply bg-white rounded-lg shadow-custom p-6;
}

.card-header {
  @apply border-b border-gray-200 pb-4 mb-4;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

.card-body {
  @apply space-y-4;
}

/* Status badges */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

.badge-secondary {
  @apply bg-gray-100 text-gray-800;
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-gray-300 border-t-primary rounded-full animate-spin;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  body {
    font-size: 12px;
    line-height: 1.4;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}

/* Custom focus styles for accessibility */
.focus-visible:focus {
  @apply outline-none ring-2 ring-primary ring-offset-2;
}

/* Sidebar submenu rotation */
.rotate-180 {
  transform: rotate(180deg);
}

/* Custom dropdown styles */
.dropdown-menu {
  @apply absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50;
}

.dropdown-item {
  @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100;
}

/* Chart container styles */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Custom alert styles */
.alert {
  @apply p-4 rounded-md border-l-4;
}

.alert-success {
  @apply bg-green-50 border-green-400 text-green-700;
}

.alert-warning {
  @apply bg-yellow-50 border-yellow-400 text-yellow-700;
}

.alert-danger {
  @apply bg-red-50 border-red-400 text-red-700;
}

.alert-info {
  @apply bg-blue-50 border-blue-400 text-blue-700;
}

/* File upload styles */
.file-upload {
  @apply relative border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors;
}

.file-upload.dragover {
  @apply border-primary bg-blue-50;
}

/* Progress bar styles */
.progress {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-bar {
  @apply bg-primary h-2 rounded-full transition-all duration-300;
}

/* Custom modal styles */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center;
}

.modal-content {
  @apply bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6;
}

/* Utility classes */
.text-currency {
  font-variant-numeric: tabular-nums;
}

.border-dashed {
  border-style: dashed;
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    /* Dark mode styles can be added here */
  }
}
